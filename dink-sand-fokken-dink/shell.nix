{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # Base development tools
    git
    curl
    
    # Python environment
    python310
    poetry
    
    # Node.js environment (if needed)
    nodejs
    yarn
    
    # Database clients (if needed)
    postgresql
    redis
    
    # Additional tools as needed
    # ...
  ];
  
  shellHook = ''
    echo "Entering development environment via shell.nix"
    
    # Set up Python environment
    export PYTHONPATH="$PWD:$PYTHONPATH"
    
    # Set up project-specific environment variables
    export PROJECT_ROOT="$PWD"
    
    # Activate virtual environment if using poetry
    if [ -d .venv ]; then
      source .venv/bin/activate
    fi
  '';
} 