# Coding Standards

## General Coding Standards

Follow these general coding standards for all code in this project:

1. Use consistent indentation (spaces, not tabs)
2. Keep line length under 100 characters
3. Use meaningful variable and function names
4. Add comments for complex logic
5. Follow language-specific style guides:
   - Python: PEP 8
   - JavaScript/TypeScript: ESLint with Airbnb or Google style
   - CSS/SCSS: Follow BEM naming convention
6. Write unit tests for all new functionality
7. Document public APIs and functions
8. Keep functions small and focused on a single responsibility
9. Use version control effectively with meaningful commit messages
10. Review code before submitting pull requests

## Error Handling

1. Never silently catch exceptions
2. Log all errors with appropriate context
3. Return meaningful error messages to users
4. Use appropriate error status codes in APIs
5. Validate all user inputs

## Security Practices

1. Sanitize all user inputs
2. Use parameterized queries for database operations
3. Implement proper authentication and authorization
4. Never store sensitive information in code or version control
5. Keep dependencies updated to avoid security vulnerabilities 