# Nix Shell Usage

## Requirements

All development for this project must be done within the Nix shell environment defined in `shell.nix`.

1. Always ensure your Cursor terminal is using the Nix shell
2. Verify the shell is active by checking terminal prompt indicators
3. Run all commands (builds, tests, etc.) within the Nix shell
4. Do not use host system binaries or dependencies

## Benefits

- Consistent development environment for all team members
- Eliminates "works on my machine" problems
- Reproducible builds and tests
- Isolated dependencies from host system
- Simplified onboarding for new developers

## Verification

To verify you are in the Nix shell, check for:

1. The shell.nix environment notification in terminal output
2. Proper versions of tools matching the project requirements
3. Access to all project-specific development tools 