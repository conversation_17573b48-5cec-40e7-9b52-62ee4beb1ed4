# GitHub PR Workflow for AI Assistants

This document outlines the standard process for creating and managing Pull Requests when AI assistants are helping with feature development.

## Pull Request Creation Process

When implementing a new feature or making other code changes, AI assistants should follow this workflow:

### 1. Branch Management

- Always create a new branch from `main` for any feature work:
  ```bash
  git checkout main
  git pull origin main
  git checkout -b feature/descriptive-feature-name
  ```
- Make regular, incremental commits with clear commit messages
- Push changes to the remote branch frequently

### 2. Large Feature Management

- For complex features that will take multiple sessions, create a temporary `todo.md` file at the start
- Use this file to track progress, necessary steps, and decisions made during implementation
- Update the todo file as work progresses to maintain clarity on remaining tasks

### 3. Pull Request Creation

- Once the feature is ready for review, create a PR using the GitHub CLI:
  ```bash
  gh pr create --title "Descriptive PR title" --body "Detailed description of changes" -r jxnl,ivanleomk -l appropriate-label
  ```
- Always include the note "This PR was written by @Cursor" in the PR description
- Add the default reviewers to all PRs:
  - Include `-r jxnl,ivanleomk` when creating the PR, or
  - Use `gh pr edit <id> --add-reviewer jxnl,ivanleomk` after creation

### 4. PR Labeling

Apply appropriate labels based on the PR content:
- `documentation`: For documentation updates or improvements
- `feature`: For new features or significant enhancements
- `client`: For client-related changes or integrations
- `types`: For type system changes or improvements

Add labels using:
```bash
gh pr edit <id> --add-label label1,label2
```
Or include `-l label1,label2` when creating the PR.

### 5. Handling PR Feedback

- Check for PR comments regularly:
  ```bash
  gh pr view <id> --comments | cat
  ```

- When addressing review feedback, follow the stacked PR pattern:
  1. Do NOT commit changes directly to the existing PR branch
  2. Instead, create a new branch from the original PR branch:
     ```bash
     git checkout feature/original-branch
     git pull origin feature/original-branch
     git checkout -b feature/original-branch-updates
     ```
  3. Make the requested changes on this new branch
  4. Create a new PR that targets the original PR branch as the base (not main):
     ```bash
     gh pr create --title "Updates for PR #<original-id>" --body "Addresses feedback from PR #<original-id>" --base feature/original-branch -r jxnl,ivanleomk
     ```
  
  This creates a "stacked PR" pattern where:
  - The original PR contains the initial changes
  - The new PR contains only the review-related updates
  - Once the base PR is merged, the stack can be rebased onto main

## PR Description Template

Include the following in all PR descriptions:

```
## Changes

[Describe key changes made]

## Testing

[Describe how the changes were tested]

## Notes

This PR was written by @Cursor

```

## PR Review Workflow

After submitting a PR:

1. Notify the user that the PR has been created, providing the PR number and link
2. Monitor for feedback using `gh pr view <id> --comments | cat`
3. Follow the stacked PR pattern for any updates needed
4. Keep the user informed about progress and any additional PRs created

## Stacked PR Maintenance

After the original PR is merged:
1. Rebase any stacked PRs onto main:
   ```bash
   git checkout feature/original-branch-updates
   git rebase main
   git push --force-with-lease
   ```
2. Update the PR base to main using the GitHub interface

Remember that this stacked PR approach makes reviews cleaner by separating initial work from updates, and maintains a clear history of how the code evolved in response to feedback.
