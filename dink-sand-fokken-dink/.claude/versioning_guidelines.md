# Version Increment Guidelines

This document outlines the versioning strategy for this project, providing clear guidelines on when and how to increment version numbers.

## Version Numbering Structure

For this project, we follow a semantic versioning approach with specific guidelines for increments:

### Version Format: MAJOR.MINOR.PATCH

- **PATCH (0.0.1)**: Increment for individual features, bug fixes, or small improvements
- **MINOR (0.1.0)**: Increment for major feature sets or significant functionality changes
- **MAJOR (1.0.0)**: Reserved for production-ready releases or breaking changes

## When to Increment Each Level

### PATCH Increments (0.0.1)

Increment the PATCH version when:
- Adding a single new feature
- Fixing a bug
- Making small improvements to existing functionality
- Adding documentation
- Refactoring code without changing behavior

Examples:
- Adding a new API endpoint
- Fixing a validation error
- Improving error messages
- Adding a new utility function

### MINOR Increments (0.1.0)

Increment the MINOR version when:
- Completing a set of related features that form a cohesive functionality
- Making significant changes that don't break existing functionality
- Adding substantial new capabilities

Examples:
- Implementing a complete authentication system
- Adding a notification subsystem
- Introducing a caching layer
- Adding a new major component to the architecture

### MAJOR Increments (1.0.0)

Increment the MAJOR version when:
- Releasing a production-ready version
- Making breaking changes to the API
- Significantly changing the architecture
- Completing a full product milestone

## Documentation Requirements

For each version increment:

1. **PATCH (0.0.1)**:
   - Update the CHANGELOG.md with a brief description of the change
   - Tag the commit with the new version number

2. **MINOR (0.1.0)**:
   - Create a detailed delta summary in `.claude/delta/` directory
   - Update the CHANGELOG.md with a comprehensive list of changes
   - Update relevant documentation
   - Tag the commit with the new version number

3. **MAJOR (1.0.0)**:
   - Create a full release document
   - Update all documentation to reflect the new version
   - Create a migration guide if necessary
   - Tag the commit with the new version number

## Implementation in CI/CD

Consider implementing these guidelines in your CI/CD pipeline:

1. Automate version bumping based on commit message prefixes:
   - `feat:` - Increment PATCH
   - `fix:` - Increment PATCH
   - `feat(major):` - Increment MINOR
   - `BREAKING CHANGE:` - Increment MAJOR

2. Automatically generate CHANGELOG entries based on commit messages

3. Enforce version increment rules through CI checks

## Example Workflow

1. Developer implements a new feature
2. Commits with message: `feat: add user profile page`
3. CI/CD pipeline increments version from 0.1.2 to 0.1.3
4. When a set of related features is complete, team leader creates a commit: `feat(major): complete user management system`
5. CI/CD pipeline increments version from 0.1.3 to 0.2.0

## Delta Summaries

For MINOR version increments, create a delta summary file in `.claude/delta/` with the naming convention `vX.Y.0_to_vX.Y+1.0.md`. This file should include:

- Overview of changes
- API changes (added, modified, removed)
- Data model changes
- Behavior changes
- Performance improvements
- Security enhancements
- Configuration changes
- Dependency updates
- Known issues
- Migration guide
- Reasoning behind significant changes

See the example in `.claude/delta/v1.0_to_v1.1.md` for the expected format and level of detail.

## Version History Tracking

All version changes should be documented in:

1. **CHANGELOG.md** - Chronological list of changes
2. **Git tags** - Each version should have a corresponding git tag
3. **Delta summaries** - For MINOR version changes
4. **Release notes** - For MAJOR version changes

## Communicating Version Changes

When a new version is released:

1. Update the version number in all relevant files (package.json, README.md, etc.)
2. Notify all team members of the new version and highlight key changes
3. For MINOR and MAJOR versions, schedule a brief review session to discuss changes

Following these guidelines will ensure consistent versioning across the project and make it easier for all team members, including Claude, to understand the evolution of the codebase. 