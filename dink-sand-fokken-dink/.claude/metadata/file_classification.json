{"version": "1.0", "classifications": {"implementation": {"description": "Files containing concrete implementations of functionality", "patterns": ["src/**/*.js", "src/**/*.ts", "src/**/*.py", "src/**/*.go", "src/**/*.java"], "excludePatterns": ["src/**/*.test.*", "src/**/*.spec.*", "src/**/*interface*.*", "src/**/*.d.ts"]}, "interface": {"description": "Files defining interfaces, types, or contracts", "patterns": ["src/**/*interface*.ts", "src/**/*.d.ts", "src/**/types/*.ts", "src/**/*Interface*.java"], "excludePatterns": ["src/**/*.test.*", "src/**/*.spec.*"]}, "test": {"description": "Test files", "patterns": ["tests/**/*", "src/**/*.test.*", "src/**/*.spec.*"], "excludePatterns": []}, "configuration": {"description": "Configuration files", "patterns": ["**/*.config.*", "**/*.json", "**/*.yml", "**/*.yaml", "**/config/**/*"], "excludePatterns": ["package.json", "package-lock.json", "node_modules/**/*"]}, "documentation": {"description": "Documentation files", "patterns": ["**/*.md", "docs/**/*", ".claude/**/*.md"], "excludePatterns": ["node_modules/**/*"]}, "build": {"description": "Build-related files", "patterns": ["**/build/**/*", "**/dist/**/*", "<PERSON><PERSON><PERSON>", "Dockerfile", "docker-compose.yml"], "excludePatterns": []}}, "files": {}}