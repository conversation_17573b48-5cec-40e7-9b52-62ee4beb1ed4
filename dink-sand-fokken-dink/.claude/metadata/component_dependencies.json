{"version": "1.0", "components": {"core": {"dependencies": [], "description": "Core functionality and utilities"}, "api": {"dependencies": ["core", "database"], "description": "API endpoints and controllers"}, "database": {"dependencies": ["core"], "description": "Database access and models"}, "ui": {"dependencies": ["core", "api"], "description": "User interface components"}, "auth": {"dependencies": ["core", "database"], "description": "Authentication and authorization"}, "notifications": {"dependencies": ["core", "database", "api"], "description": "Notification system"}}, "relationships": [{"from": "api", "to": "core", "type": "uses"}, {"from": "api", "to": "database", "type": "uses"}, {"from": "ui", "to": "api", "type": "consumes"}, {"from": "ui", "to": "core", "type": "uses"}, {"from": "auth", "to": "core", "type": "uses"}, {"from": "auth", "to": "database", "type": "uses"}, {"from": "notifications", "to": "core", "type": "uses"}, {"from": "notifications", "to": "database", "type": "uses"}, {"from": "notifications", "to": "api", "type": "extends"}]}