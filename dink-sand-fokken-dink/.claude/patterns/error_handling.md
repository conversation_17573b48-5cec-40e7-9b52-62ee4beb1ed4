# Error Handling Patterns

This document outlines the canonical error handling patterns used in this project, with a focus on context preservation and proper error propagation.

## Error Types

### ApplicationError

The base error class for all application-specific errors. Extends the standard `Error` class and adds context preservation.

```javascript
// src/core/errors/ApplicationError.js
class ApplicationError extends Error {
  constructor(message, options = {}) {
    super(message);
    this.name = this.constructor.name;
    this.status = options.status || 500;
    this.code = options.code || 'INTERNAL_ERROR';
    this.context = options.context || {};
    this.cause = options.cause;
    
    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
    
    // If this error wraps another error, append its stack
    if (this.cause && this.cause instanceof Error) {
      this.stack = `${this.stack}\nCaused by: ${this.cause.stack}`;
    }
  }
  
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      status: this.status,
      context: this.context
    };
  }
}

module.exports = ApplicationError;
```

### Specific Error Types

Extend `ApplicationError` for specific error categories:

```javascript
// src/core/errors/ValidationError.js
const ApplicationError = require('./ApplicationError');

class ValidationError extends ApplicationError {
  constructor(message, options = {}) {
    super(message, {
      status: 400,
      code: options.code || 'VALIDATION_ERROR',
      context: options.context,
      cause: options.cause
    });
    this.validationErrors = options.validationErrors || [];
  }
  
  toJSON() {
    return {
      ...super.toJSON(),
      validationErrors: this.validationErrors
    };
  }
}

module.exports = ValidationError;
```

## Error Handling Patterns

### Try-Catch with Context Preservation

```javascript
async function processUserData(userData) {
  try {
    const validatedData = validateUserData(userData);
    const user = await saveUser(validatedData);
    return user;
  } catch (error) {
    // Add context to the error
    if (error instanceof ValidationError) {
      throw error; // Already has context
    } else if (error.code === 'ER_DUP_ENTRY') {
      throw new ApplicationError('User already exists', {
        status: 409,
        code: 'USER_ALREADY_EXISTS',
        context: { email: userData.email },
        cause: error
      });
    } else {
      throw new ApplicationError('Failed to process user data', {
        code: 'USER_PROCESSING_FAILED',
        context: { userId: userData.id },
        cause: error
      });
    }
  }
}
```

### Async Error Handling with Promise Chains

```javascript
function processOrder(orderData) {
  return validateOrder(orderData)
    .then(validatedOrder => createOrder(validatedOrder))
    .then(order => processPayment(order))
    .then(order => sendOrderConfirmation(order))
    .catch(error => {
      if (error instanceof PaymentError) {
        // Payment-specific error handling
        notifyPaymentFailure(orderData, error);
        throw error;
      } else {
        // Add context and rethrow
        throw new ApplicationError('Order processing failed', {
          code: 'ORDER_PROCESSING_FAILED',
          context: { orderId: orderData.id },
          cause: error
        });
      }
    });
}
```

## Error Middleware

Central error handling middleware for API requests:

```javascript
// src/api/middleware/errorHandler.js
const ApplicationError = require('../../core/errors/ApplicationError');
const logger = require('../../core/logger');

function errorHandler(err, req, res, next) {
  // Log the error with context
  logger.error('Request error', {
    error: err,
    url: req.url,
    method: req.method,
    body: req.body,
    params: req.params,
    query: req.query,
    user: req.user ? req.user.id : 'anonymous'
  });
  
  // Handle ApplicationError instances
  if (err instanceof ApplicationError) {
    return res.status(err.status).json({
      error: err.toJSON()
    });
  }
  
  // Handle unknown errors
  const status = err.status || 500;
  const response = {
    error: {
      name: 'InternalServerError',
      message: process.env.NODE_ENV === 'production' 
        ? 'An unexpected error occurred' 
        : err.message,
      code: 'INTERNAL_ERROR',
      status
    }
  };
  
  return res.status(status).json(response);
}

module.exports = errorHandler;
```

## Best Practices

1. **Always preserve context**: When catching and rethrowing errors, include relevant context information.
2. **Use specific error types**: Create specific error classes for different error categories.
3. **Include error codes**: Use consistent error codes for easier error identification and handling.
4. **Chain error causes**: When wrapping errors, include the original error as the cause.
5. **Centralize error handling**: Use middleware or global error handlers to ensure consistent error responses.
6. **Log with context**: Include request and user context when logging errors.
7. **Sanitize error messages**: Don't expose sensitive information in error messages sent to clients.

## Anti-patterns to Avoid

1. ❌ **Swallowing errors**: Never catch errors without proper handling or rethrowing.
2. ❌ **Generic error messages**: Avoid generic messages like "An error occurred" without context.
3. ❌ **Exposing stack traces**: Never send stack traces to clients in production.
4. ❌ **Inconsistent error formats**: Maintain a consistent error response format.
5. ❌ **Missing error logging**: Always log errors with appropriate severity and context.

## Error Handling Decision Tree

1. Is this a known, expected error condition?
   - Yes: Use a specific error type with appropriate status code
   - No: Continue

2. Can you add context to make the error more understandable?
   - Yes: Wrap in ApplicationError with context
   - No: Continue

3. Is this a critical error that requires immediate attention?
   - Yes: Log as critical, notify on-call, and return 500
   - No: Log as error and return appropriate status code

4. Should the client retry the request?
   - Yes: Include Retry-After header and return 503
   - No: Return appropriate final status code 