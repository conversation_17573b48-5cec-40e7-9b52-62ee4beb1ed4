{"version": "1.0", "intents": {"data_validation": {"description": "Code that validates input data for correctness and security", "patterns": ["validate", "sanitize", "check", "<PERSON><PERSON><PERSON><PERSON>", "schema.validate"], "locations": [{"file": "src/api/middleware/validation.js", "lineStart": 5, "lineEnd": 30, "description": "Input validation middleware for API requests"}, {"file": "src/core/utils/validators.js", "lineStart": 10, "lineEnd": 50, "description": "Common validation utilities"}]}, "error_handling": {"description": "Code that handles errors and exceptions", "patterns": ["try/catch", "catch(", "error", "exception", "throw new"], "locations": [{"file": "src/api/middleware/errorHandler.js", "lineStart": 5, "lineEnd": 25, "description": "Global API error handler middleware"}, {"file": "src/core/utils/errorUtils.js", "lineStart": 5, "lineEnd": 30, "description": "Error utilities for consistent error handling"}]}, "authentication": {"description": "Code that handles user authentication", "patterns": ["login", "authenticate", "jwt", "token", "password"], "locations": [{"file": "src/auth/services/authService.js", "lineStart": 10, "lineEnd": 50, "description": "Authentication service implementation"}, {"file": "src/api/middleware/authenticate.js", "lineStart": 5, "lineEnd": 25, "description": "Authentication middleware"}]}, "data_persistence": {"description": "Code that saves or retrieves data from persistent storage", "patterns": ["save", "find", "update", "delete", "query", "repository"], "locations": [{"file": "src/database/repositories/baseRepository.js", "lineStart": 5, "lineEnd": 50, "description": "Base repository implementation for data access"}, {"file": "src/database/models/", "description": "Database models directory"}]}, "business_logic": {"description": "Core business logic implementation", "patterns": ["service", "process", "calculate", "business", "rule"], "locations": [{"file": "src/core/services/", "description": "Core services directory containing business logic"}, {"file": "src/api/controllers/", "description": "API controllers implementing business operations"}]}, "ui_rendering": {"description": "Code that renders user interface elements", "patterns": ["render", "component", "view", "template", "display"], "locations": [{"file": "src/ui/components/", "description": "UI component directory"}, {"file": "src/ui/pages/", "description": "Page components directory"}]}}}