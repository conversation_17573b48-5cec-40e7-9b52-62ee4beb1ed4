{"version": "1.0", "types": {"core.interfaces.ILogger": {"file": "src/core/interfaces/ILogger.ts", "kind": "interface", "methods": ["log", "error", "warn", "info", "debug"], "properties": ["level"], "implementedBy": ["core.services.ConsoleLogger", "core.services.FileLogger"], "extendedBy": ["core.interfaces.IStructuredLogger"]}, "core.interfaces.IStructuredLogger": {"file": "src/core/interfaces/IStructuredLogger.ts", "kind": "interface", "methods": ["logWithContext", "logWithMetadata"], "properties": ["contextFields"], "implementedBy": ["core.services.StructuredConsoleLogger"], "extends": ["core.interfaces.ILogger"]}, "core.services.ConsoleLogger": {"file": "src/core/services/ConsoleLogger.ts", "kind": "class", "implements": ["core.interfaces.ILogger"], "methods": ["log", "error", "warn", "info", "debug"], "properties": ["level"]}, "core.services.FileLogger": {"file": "src/core/services/FileLogger.ts", "kind": "class", "implements": ["core.interfaces.ILogger"], "methods": ["log", "error", "warn", "info", "debug", "setLogFile"], "properties": ["level", "logFilePath"]}, "core.services.StructuredConsoleLogger": {"file": "src/core/services/StructuredConsoleLogger.ts", "kind": "class", "implements": ["core.interfaces.IStructuredLogger"], "methods": ["log", "error", "warn", "info", "debug", "logWithContext", "logWithMetadata"], "properties": ["level", "contextFields"]}, "database.interfaces.IRepository": {"file": "src/database/interfaces/IRepository.ts", "kind": "interface", "methods": ["findById", "findAll", "create", "update", "delete"], "properties": [], "implementedBy": ["database.repositories.UserRepository", "database.repositories.EventRepository"]}, "database.repositories.UserRepository": {"file": "src/database/repositories/UserRepository.ts", "kind": "class", "implements": ["database.interfaces.IRepository"], "methods": ["findById", "findAll", "create", "update", "delete", "findByEmail"], "properties": ["model"]}, "database.repositories.EventRepository": {"file": "src/database/repositories/EventRepository.ts", "kind": "class", "implements": ["database.interfaces.IRepository"], "methods": ["findById", "findAll", "create", "update", "delete", "findByDate", "findByOrganizer"], "properties": ["model"]}}}