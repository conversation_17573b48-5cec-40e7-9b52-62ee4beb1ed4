# Delta Summary: v1.0 to v1.1

## Overview

This document summarizes the semantic changes between v1.0 and v1.1 of the project. Rather than focusing on line-by-line code changes, this summary highlights the functional differences, API changes, and behavioral modifications that are important for understanding the evolution of the system.

## API Changes

### Added Endpoints

| Method | Path | Description | Breaking Change |
|--------|------|-------------|----------------|
| GET | /api/events/featured | Retrieve featured events | No |
| POST | /api/events/:id/register | Register for an event | No |
| GET | /api/users/:id/events | Get events for a user | No |

### Modified Endpoints

| Method | Path | Change | Breaking Change |
|--------|------|--------|----------------|
| GET | /api/events | Added pagination support | No |
| GET | /api/events | Added filtering by category | No |
| POST | /api/auth/login | Now returns refresh token | No |
| PUT | /api/users/:id | Now requires authentication | Yes |

### Removed Endpoints

| Method | Path | Replacement | Breaking Change |
|--------|------|-------------|----------------|
| GET | /api/legacy/events | Use /api/events instead | Yes |
| POST | /api/users/search | Use GET /api/users with query params | Yes |

## Data Model Changes

### User Model

- Added `lastLoginAt` timestamp field
- Added `preferences` JSON object field
- Changed `role` field from string to string array (breaking change)
- Removed `temporaryToken` field (breaking change)

### Event Model

- Added `category` string field
- Added `featured` boolean field
- Added `registrationDeadline` date field
- Modified `location` to be a structured object instead of string (breaking change)

## Behavior Changes

### Authentication

- JWT tokens now expire after 1 hour instead of 24 hours
- Implemented refresh token flow for obtaining new access tokens
- Added rate limiting for failed login attempts (5 attempts per 15 minutes)
- Password reset tokens now expire after 1 hour instead of 24 hours

### Event Registration

- Users can now register for events
- Event creators receive email notifications when users register
- Featured events are highlighted in the events list
- Events with registration deadlines prevent late registrations

### User Management

- User preferences are now persisted across sessions
- User activity is tracked with last login timestamp
- Users can have multiple roles instead of a single role
- Email verification is now required before account activation

## Performance Improvements

- Implemented database connection pooling
- Added Redis caching for frequently accessed data
- Optimized event queries with database indexes
- Implemented lazy loading for event attendees

## Security Enhancements

- Implemented CSRF protection for all state-changing operations
- Added Content-Security-Policy headers
- Strengthened password requirements (min 10 chars, mixed case, numbers, symbols)
- Implemented IP-based rate limiting for all API endpoints

## Configuration Changes

| Name | Old Default | New Default | Purpose | Breaking Change |
|------|-------------|-------------|---------|----------------|
| `AUTH_TOKEN_EXPIRY` | `86400` (24h) | `3600` (1h) | JWT token lifetime | Yes |
| `REDIS_CACHE_ENABLED` | `false` | `true` | Enable Redis caching | No |
| `PASSWORD_MIN_LENGTH` | `8` | `10` | Minimum password length | Yes |
| `RATE_LIMIT_WINDOW` | `900000` (15m) | `300000` (5m) | Rate limit window | No |

## Dependency Updates

- Updated Express from 4.17.1 to 4.18.2
- Updated Sequelize from 6.6.5 to 6.32.1
- Added `redis` package (v4.6.7) for caching
- Added `rate-limiter-flexible` package (v2.4.1) for rate limiting

## Known Issues

- Event registration may fail under high concurrent load (#143)
- User preferences occasionally don't persist in Safari browsers (#156)
- Redis cache invalidation can be delayed up to 30 seconds (#162)

## Migration Guide

### Breaking Changes Migration

1. **Role Field Change**:
   - Update client code to handle role as array
   - Migrate database: `UPDATE users SET role = JSON_ARRAY(role) WHERE JSON_TYPE(role) = 'STRING'`

2. **Location Field Change**:
   - Update client code to handle structured location object
   - Migrate database: `UPDATE events SET location = JSON_OBJECT('address', location, 'coordinates', NULL) WHERE JSON_TYPE(location) = 'STRING'`

3. **Token Expiry**:
   - Update client applications to handle more frequent token refreshes
   - Implement refresh token flow in clients

### Configuration Updates

```bash
# Update environment variables
export AUTH_TOKEN_EXPIRY=3600
export REDIS_CACHE_ENABLED=true
export PASSWORD_MIN_LENGTH=10
export RATE_LIMIT_WINDOW=300000
```

### Database Migrations

Run the following migration scripts:

```bash
npm run migrate:up
```

This will apply the following changes:
- Add new fields to User and Event tables
- Create indexes for optimized queries
- Modify existing fields for new data types

## Reasoning Behind Changes

### Shorter Token Expiry

The JWT token expiry was reduced from 24 hours to 1 hour to improve security by limiting the window of opportunity for token misuse. This change follows security best practices for sensitive operations, though it requires implementing a refresh token flow to maintain user sessions.

### Role Array Change

Changing the role field from a string to an array enables more granular permission control and supports users with multiple responsibilities. This better reflects organizational realities where users often have overlapping roles.

### Redis Caching

Redis caching was implemented to reduce database load and improve response times for frequently accessed data. Performance testing showed a 60% reduction in database queries and a 45% improvement in average response time for common operations. 