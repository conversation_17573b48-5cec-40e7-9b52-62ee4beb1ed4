# Documentation Standards

This document outlines the documentation standards for the project, including specific guidelines for using Mermaid diagrams for visualizations.

## General Documentation Guidelines

1. All documentation should be written in Markdown
2. Keep documentation up-to-date as code changes
3. Use consistent formatting across all documents
4. Include examples where appropriate
5. Link to related documentation when relevant
6. Break long documents into logical sections with headings
7. Use tables for structured data
8. Include version information or last updated date at the bottom of documents

## Required Documentation

Every project should include at a minimum:

1. README.md with project overview and getting started information
2. Architecture documentation
3. API documentation
4. Database schema documentation
5. Setup and installation instructions
6. Troubleshooting guide
7. Deployment guide

## Mermaid Diagrams

All diagrams in the project documentation should use Mermaid, which allows for creating diagrams using text and code. This approach:

- Keeps diagrams in sync with the codebase
- Allows version control of diagrams
- Makes diagrams accessible without specialized software
- Ensures consistent styling across the project

### Basic Mermaid Usage

Embed Mermaid diagrams in Markdown using the following syntax:

```mermaid
// diagram code here
```

### Diagram Types and Examples

#### 1. Flowcharts

Use flowcharts to represent processes, algorithms, or decision flows.

```mermaid
graph TD
    A[Start] --> B{Is session valid?}
    B -->|Yes| C[Process request]
    B -->|No| D[Redirect to login]
    C --> E[Return response]
    D --> F[Show login form]
```

```
graph TD
    A[Start] --> B{Is session valid?}
    B -->|Yes| C[Process request]
    B -->|No| D[Redirect to login]
    C --> E[Return response]
    D --> F[Show login form]
```

#### 2. Sequence Diagrams

Use sequence diagrams to represent interactions between components.

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Database
    
    Client->>API: Authentication Request
    API->>Database: Validate Credentials
    Database-->>API: Validation Result
    API-->>Client: Authentication Response
```

```
sequenceDiagram
    participant Client
    participant API
    participant Database
    
    Client->>API: Authentication Request
    API->>Database: Validate Credentials
    Database-->>API: Validation Result
    API-->>Client: Authentication Response
```

#### 3. Class Diagrams

Use class diagrams to represent data models and their relationships.

```mermaid
classDiagram
    User "1" -- "n" Order : places
    Order "1" -- "n" OrderItem : contains
    Product "1" -- "n" OrderItem : included in
    
    class User {
        +id: UUID
        +email: String
        +password: String
        +createOrder()
    }
    
    class Order {
        +id: UUID
        +userId: UUID
        +createdAt: DateTime
        +total: Decimal
        +addItem()
    }
    
    class OrderItem {
        +id: UUID
        +orderId: UUID
        +productId: UUID
        +quantity: Integer
        +price: Decimal
    }
    
    class Product {
        +id: UUID
        +name: String
        +price: Decimal
        +inStock: Boolean
    }
```

```
classDiagram
    User "1" -- "n" Order : places
    Order "1" -- "n" OrderItem : contains
    Product "1" -- "n" OrderItem : included in
    
    class User {
        +id: UUID
        +email: String
        +password: String
        +createOrder()
    }
    
    class Order {
        +id: UUID
        +userId: UUID
        +createdAt: DateTime
        +total: Decimal
        +addItem()
    }
    
    class OrderItem {
        +id: UUID
        +orderId: UUID
        +productId: UUID
        +quantity: Integer
        +price: Decimal
    }
    
    class Product {
        +id: UUID
        +name: String
        +price: Decimal
        +inStock: Boolean
    }
```

#### 4. Entity Relationship Diagrams

Use ERDs to represent database schema.

```mermaid
erDiagram
    USERS ||--o{ ORDERS : places
    ORDERS ||--|{ ORDER_ITEMS : contains
    PRODUCTS ||--o{ ORDER_ITEMS : "ordered in"
    
    USERS {
        uuid id PK
        string email
        string password_hash
        timestamp created_at
    }
    
    ORDERS {
        uuid id PK
        uuid user_id FK
        decimal total
        string status
        timestamp created_at
    }
    
    ORDER_ITEMS {
        uuid id PK
        uuid order_id FK
        uuid product_id FK
        int quantity
        decimal price
    }
    
    PRODUCTS {
        uuid id PK
        string name
        string description
        decimal price
        int stock
    }
```

```
erDiagram
    USERS ||--o{ ORDERS : places
    ORDERS ||--|{ ORDER_ITEMS : contains
    PRODUCTS ||--o{ ORDER_ITEMS : "ordered in"
    
    USERS {
        uuid id PK
        string email
        string password_hash
        timestamp created_at
    }
    
    ORDERS {
        uuid id PK
        uuid user_id FK
        decimal total
        string status
        timestamp created_at
    }
    
    ORDER_ITEMS {
        uuid id PK
        uuid order_id FK
        uuid product_id FK
        int quantity
        decimal price
    }
    
    PRODUCTS {
        uuid id PK
        string name
        string description
        decimal price
        int stock
    }
```

#### 5. State Diagrams

Use state diagrams to represent state machines and workflows.

```mermaid
stateDiagram-v2
    [*] --> Pending
    Pending --> Processing: approve
    Pending --> Rejected: reject
    Processing --> Shipping: process complete
    Shipping --> Delivered: delivered
    Rejected --> [*]
    Delivered --> [*]
```

```
stateDiagram-v2
    [*] --> Pending
    Pending --> Processing: approve
    Pending --> Rejected: reject
    Processing --> Shipping: process complete
    Shipping --> Delivered: delivered
    Rejected --> [*]
    Delivered --> [*]
```

#### 6. Gantt Charts

Use Gantt charts for project timelines and scheduling.

```mermaid
gantt
    title Project Timeline
    dateFormat  YYYY-MM-DD
    
    section Planning
    Requirements Analysis   :a1, 2023-01-01, 10d
    System Design           :a2, after a1, 15d
    
    section Development
    Frontend Implementation :b1, after a2, 20d
    Backend Implementation  :b2, after a2, 25d
    Integration             :b3, after b1 b2, 10d
    
    section Testing
    Unit Testing            :c1, after b3, 8d
    System Testing          :c2, after c1, 10d
    User Acceptance Testing :c3, after c2, 7d
    
    section Deployment
    Staging Deployment      :d1, after c3, 3d
    Production Deployment   :d2, after d1, 2d
```

```
gantt
    title Project Timeline
    dateFormat  YYYY-MM-DD
    
    section Planning
    Requirements Analysis   :a1, 2023-01-01, 10d
    System Design           :a2, after a1, 15d
    
    section Development
    Frontend Implementation :b1, after a2, 20d
    Backend Implementation  :b2, after a2, 25d
    Integration             :b3, after b1 b2, 10d
    
    section Testing
    Unit Testing            :c1, after b3, 8d
    System Testing          :c2, after c1, 10d
    User Acceptance Testing :c3, after c2, 7d
    
    section Deployment
    Staging Deployment      :d1, after c3, 3d
    Production Deployment   :d2, after d1, 2d
```

### When to Use Different Diagram Types

| Diagram Type | When to Use |
|--------------|-------------|
| Flowchart | For algorithms, processes, decision trees |
| Sequence Diagram | For component interactions, API flows, message passing |
| Class Diagram | For data models, class relationships, OOP design |
| ER Diagram | For database schema, table relationships |
| State Diagram | For state machines, workflows, status transitions |
| Gantt Chart | For project planning, timelines, scheduling |

### Best Practices for Mermaid Diagrams

1. **Simplicity**: Keep diagrams focused on one concept or flow
2. **Consistency**: Use consistent naming and styling across diagrams
3. **Clear Labels**: Use descriptive labels for nodes and connections
4. **Direction**: Use a consistent flow direction (usually top-to-bottom or left-to-right)
5. **Documentation**: Add comments within the diagram code for complex parts
6. **Testing**: Verify that the diagram renders correctly
7. **Modularity**: Break complex diagrams into smaller, focused diagrams

## Documentation Reviews

Documentation should be reviewed as part of the development process:

1. Technical accuracy
2. Completeness
3. Clarity and understandability
4. Proper diagram usage
5. Consistency with other documentation

## Documentation Location Guidelines

Place documentation in the appropriate locations:

- Project overview, setup instructions: `README.md`
- Architecture and technical design: `.claude/architecture.md`
- API documentation: `.claude/api_endpoints.md`
- Database schema: `.claude/database_schema.md`
- UI components: `.claude/ui_components.md`
- Troubleshooting guides: `.claude/troubleshooting.md`
- User-facing documentation: `docs/`

---

Last Updated: [Date] 