# Onboarding Guide

This document provides a comprehensive guide for new developers (human or AI) to get up to speed quickly with this project.

## Purpose

This onboarding guide helps new team members understand the project structure, development workflow, and key concepts. It provides a step-by-step approach to becoming productive on the project as quickly as possible.

## Project Overview

[Brief description of the project, its purpose, and key features]

### Key Technologies

- **Backend**: [e.g., Python, Flask, SQLAlchemy]
- **Frontend**: [e.g., React, Redux, TypeScript]
- **Database**: [e.g., PostgreSQL, MongoDB]
- **Infrastructure**: [e.g., Docker, Kubernetes, AWS]
- **Testing**: [e.g., pytest, Jest, Cypress]

## Getting Started

### 1. Environment Setup

1. **Clone the repository**:
   ```bash
   git clone [repository-url]
   cd [project-name]
   ```

2. **Set up development environment**:
   - Install required tools:
     - [Tool 1] (version X.Y.Z)
     - [Tool 2] (version X.Y.Z)
     - [Tool 3] (version X.Y.Z)
   
3. **Install dependencies**:
   ```bash
   # For Python
   pip install -r requirements.txt
   
   # For JavaScript
   npm install
   ```

4. **Configure environment variables**:
   - Copy `.env.example` to `.env`
   - Update values as needed

5. **Set up the database**:
   ```bash
   # Run migrations
   flask db upgrade
   
   # Seed initial data
   flask seed
   ```

6. **Start the development server**:
   ```bash
   # Backend
   flask run
   
   # Frontend
   npm start
   ```

### 2. Project Structure

```
project/
├── .claude/                  # Claude AI assistant configuration
├── .cursor/                  # Cursor IDE configuration
├── .dockerwrapper/           # Docker configuration
├── docs/                     # Project documentation
├── src/                      # Source code
│   ├── api/                  # API endpoints
│   ├── models/               # Data models
│   ├── services/             # Business logic
│   ├── utils/                # Utility functions
│   └── frontend/             # Frontend code
├── tests/                    # Test suite
│   ├── unit/                 # Unit tests
│   ├── integration/          # Integration tests
│   └── e2e/                  # End-to-end tests
├── CHANGELOG.md              # Project changelog
└── README.md                 # Project README
```

### 3. Development Workflow

1. **Feature Branch Workflow**:
   - Create a feature branch from `main`
   - Implement the feature
   - Write tests
   - Submit a pull request
   - Address review comments
   - Merge to `main`

2. **Commit Guidelines**:
   - Use conventional commit messages
   - Reference issue numbers in commits
   - Keep commits focused and atomic

3. **Code Review Process**:
   - All code changes require review
   - Address all comments before merging
   - Ensure tests pass before requesting review

4. **Testing Requirements**:
   - Write unit tests for all new code
   - Ensure all tests pass before submitting PR
   - Maintain or improve code coverage

## Key Concepts

### 1. Architecture

[Brief description of the application architecture]

See [.claude/architecture.md](.claude/architecture.md) for detailed architecture documentation.

### 2. Data Model

[Brief description of the core data models and relationships]

See [.claude/database_schema.md](.claude/database_schema.md) for detailed schema documentation.

### 3. API Design

[Brief description of the API design principles]

See [.claude/api_endpoints.md](.claude/api_endpoints.md) for detailed API documentation.

### 4. Authentication and Authorization

[Brief description of the authentication and authorization system]

### 5. Key Libraries and Dependencies

[Brief description of the most important libraries and dependencies]

See [.claude/dependencies.md](.claude/dependencies.md) for detailed dependency documentation.

## Common Tasks

### 1. Adding a New API Endpoint

1. Define the route in the appropriate router file
2. Implement the controller function
3. Add validation for request data
4. Implement error handling
5. Write tests for the endpoint
6. Update API documentation

### 2. Adding a New Database Model

1. Create the model class in the appropriate file
2. Define relationships to other models
3. Create a migration for the new model
4. Update the repository layer
5. Write tests for the model and repository
6. Update schema documentation

### 3. Adding a New Frontend Component

1. Create the component file
2. Implement the component logic
3. Add styles for the component
4. Write tests for the component
5. Update component documentation

### 4. Running Tests

```bash
# Run all tests
pytest

# Run specific test file
pytest tests/path/to/test_file.py

# Run frontend tests
npm test
```

### 5. Database Migrations

```bash
# Create a new migration
flask db migrate -m "Description of changes"

# Apply migrations
flask db upgrade

# Rollback migration
flask db downgrade
```

## Troubleshooting

### Common Issues

1. **Issue 1**:
   - Symptoms: [description]
   - Solution: [steps to resolve]

2. **Issue 2**:
   - Symptoms: [description]
   - Solution: [steps to resolve]

3. **Issue 3**:
   - Symptoms: [description]
   - Solution: [steps to resolve]

See [.claude/troubleshooting.md](.claude/troubleshooting.md) for more detailed troubleshooting guidance.

## Resources

### Documentation

- [Project Documentation](docs/)
- [API Documentation](docs/api/)
- [Architecture Documentation](.claude/architecture.md)

### Learning Resources

- [Resource 1]: [description and link]
- [Resource 2]: [description and link]
- [Resource 3]: [description and link]

### Team Communication

- **Issue Tracker**: [link]
- **Chat**: [link]
- **Email**: [email address]

## For AI Assistants

If you're an AI assistant (like Claude) working on this project:

1. **Review the .claude directory** for project-specific guidance
2. **Understand the architecture** before suggesting changes
3. **Follow the code review guidelines** when reviewing code
4. **Reference the decision log** to understand past decisions
5. **Use the glossary** to understand domain-specific terminology
6. **Follow the testing strategy** when suggesting tests
7. **Adhere to the security checklist** when implementing features
8. **Consider performance guidelines** when optimizing code

## Nix Development Environment

This project uses a Nix shell for development to ensure consistent environments across all developers and CI/CD systems.

### Setting Up Nix

1. **Install Nix**:
   ```bash
   curl -L https://nixos.org/nix/install | sh
   ```

2. **Enter the Nix shell**:
   ```bash
   nix-shell
   ```

3. **Cursor Integration**:
   Cursor IDE is configured to automatically use the Nix shell environment. When you open a terminal in Cursor, it should automatically start in the Nix shell. If it doesn't, you can manually enter the shell with `nix-shell`.

### Benefits of using Nix

- Consistent tooling versions across all developers
- Isolated development environment
- Reproducible builds
- Simplified dependency management
- No conflicts with host system packages

---

*Note: This onboarding guide should be kept up-to-date as the project evolves. If you find any outdated information, please update it or notify the team.*