{"id": "DEBUG001", "title": "Authentication Token Expiry Issue", "date": "2023-06-15", "component": "auth", "errorType": "authentication", "error": {"message": "JWT token has expired", "stack": "Error: JWT token has expired\n    at verifyToken (/src/auth/utils/tokenUtils.js:45:13)\n    at authenticate (/src/api/middleware/authenticate.js:12:23)\n    at processRequest (/src/api/middleware/index.js:25:12)", "context": {"tokenAge": "3h 15m", "tokenExpiryConfig": "3h", "endpoint": "/api/users/profile"}}, "solution": {"description": "The token expiry check was using local time instead of UTC, causing tokens to expire prematurely in certain timezones", "fixes": [{"file": "src/auth/utils/tokenUtils.js", "lineStart": 40, "lineEnd": 50, "before": "const isExpired = expiryTimestamp < Date.now();", "after": "const isExpired = expiryTimestamp < Math.floor(Date.now() / 1000);", "explanation": "JWT expiry timestamps are in seconds, not milliseconds. Date.now() returns milliseconds, so we need to convert."}], "verification": {"steps": ["Generate a new token with 3h expiry", "Verify token works for protected endpoints", "Set system clock forward 2h 55m", "Verify token still works", "Set system clock forward 10m more (total 3h 5m)", "Verify token is now rejected with proper expiry message"], "result": "Token now expires exactly at the configured expiry time"}}, "relatedIssues": ["DEBUG005", "DEBUG012"], "tags": ["jwt", "authentication", "timezone", "expiry"], "codeVersion": {"commit": "a1b2c3d4e5f6", "branch": "main"}}