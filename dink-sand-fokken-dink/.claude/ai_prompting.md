# AI Prompting Guide

This document provides guidance on how to effectively prompt <PERSON> for assistance with this project. It includes examples of well-structured prompts for common tasks.

## Purpose

Effective prompting helps <PERSON> provide more accurate, relevant, and useful responses. This guide helps team members craft prompts that clearly communicate their needs and context to <PERSON>.

## General Prompting Principles

1. **Be Specific**: Clearly state what you need
2. **Provide Context**: Include relevant background information
3. **Set Expectations**: Specify the format or level of detail you want
4. **Use Examples**: Show examples of desired output when possible
5. **Break Down Complex Tasks**: Split complex requests into smaller steps
6. **Iterate**: Refine your prompt based on <PERSON>'s responses

## Prompt Structure

A well-structured prompt typically includes:

1. **Task Description**: What you want <PERSON> to do
2. **Context**: Relevant background information
3. **Constraints**: Any limitations or requirements
4. **Format**: How you want the response structured
5. **Examples**: Sample outputs (if applicable)

## Example Prompts by Task

### Code Implementation

```
Task: Implement a function to [specific functionality].

Context: This is part of the [component/module] which handles [purpose]. It needs to integrate with [related components].

Requirements:
- The function should [specific behavior]
- It needs to handle [edge cases]
- Follow our [specific coding standards]
- Include error handling for [specific scenarios]

Example usage:
```python
# How the function would be called
result = my_function(param1, param2)
```

Please include unit tests and documentation.
```

### Code Review

```
Task: Review this code for issues and improvements.

Code:
```python
def calculate_total(items):
    total = 0
    for item in items:
        total += item.price * item.quantity
    return total
```

Focus areas:
- Performance considerations
- Error handling
- Edge cases
- Adherence to our coding standards
- Potential bugs

Please provide specific suggestions for improvements with code examples.
```

### Debugging

```
Task: Help debug an issue with [specific functionality].

Error message:
```
[Paste exact error message]
```

Context:
- This occurs when [specific conditions]
- The expected behavior is [description]
- I've already tried [previous attempts]

Relevant code:
```python
# Paste the relevant code snippet
```

What might be causing this issue and how can I fix it?
```

### Architecture Design

```
Task: Help design the architecture for [specific feature/component].

Requirements:
- The feature needs to [specific functionality]
- It will interact with [existing components]
- Performance considerations include [specific requirements]
- Security considerations include [specific requirements]

Current architecture:
[Brief description or diagram of current architecture]

Please suggest an architecture approach with:
- Component diagram (using Mermaid)
- Key interfaces
- Data flow
- Considerations for scalability and maintainability
```

### Database Query Optimization

```
Task: Optimize this database query for performance.

Current query:
```sql
SELECT * FROM users 
JOIN orders ON users.id = orders.user_id
WHERE orders.status = 'completed'
```

Context:
- The users table has approximately [number] rows
- The orders table has approximately [number] rows
- This query is used in [specific context]
- Current execution time is [time]

Please suggest optimizations with explanations of why they would improve performance.
```

### Test Case Generation

```
Task: Generate test cases for [specific functionality].

Functionality description:
[Description of what the code does]

Code to test:
```python
# Code snippet to be tested
```

Please include:
- Unit tests covering happy paths
- Tests for edge cases
- Tests for error conditions
- Any necessary mocks or fixtures
```

### Documentation Writing

```
Task: Create documentation for [specific component/API/feature].

Component purpose:
[Brief description of what it does]

Key functionality:
- [Function/feature 1]
- [Function/feature 2]
- [Function/feature 3]

Target audience:
[Developers/End users/Administrators]

Please structure the documentation with:
- Overview
- Installation/Setup
- Usage examples
- API reference
- Troubleshooting
```

### Refactoring

```
Task: Refactor this code to improve [specific aspect].

Current code:
```python
# Code snippet to refactor
```

Issues to address:
- [Specific issue 1]
- [Specific issue 2]
- [Specific issue 3]

Constraints:
- Must maintain the same functionality
- Must be compatible with [specific dependencies]
- Should follow [specific patterns or principles]

Please explain your refactoring decisions.
```

## Project-Specific Prompting

### Feature Implementation

```
Task: Help implement the [feature name] as described in .claude/feature_plans/[feature_plan.md].

Current status:
- [What's already been implemented]
- [What's still needed]

Specific assistance needed:
- [Specific aspect you need help with]

Please follow our feature branch workflow as described in .claude/feature_branch_workflow.md.
```

### Architecture Questions

```
Task: Explain how [specific component] fits into our overall architecture.

I'm trying to understand:
- [Specific question about the architecture]
- [Another specific question]

Please reference the architecture documentation in .claude/architecture.md in your explanation.
```

### Troubleshooting Project-Specific Issues

```
Task: Help troubleshoot [specific issue] with [component].

Error/Issue:
[Description of the problem]

I've checked the troubleshooting guide (.claude/troubleshooting.md) and tried:
- [Solution 1 already attempted]
- [Solution 2 already attempted]

Relevant configuration:
[Any relevant configuration details]

What else should I try?
```

## Iterative Prompting

If Claude's response isn't exactly what you need, iterate with follow-up prompts:

1. **Clarify**: "To clarify, I'm looking for [specific aspect]."
2. **Expand**: "Could you expand on [specific point]?"
3. **Redirect**: "That's helpful, but I'm more interested in [different aspect]."
4. **Simplify**: "Could you explain that in simpler terms?"
5. **Examples**: "Could you provide an example of [concept]?"

## Tips for Specific Roles

### For Developers

- Include code snippets when asking about code
- Specify programming language and framework versions
- Reference specific files or components in the codebase
- Mention relevant architectural constraints

### For Designers

- Include mockups or wireframes when discussing UI/UX
- Specify design system constraints
- Reference user personas or user stories
- Clarify accessibility requirements

### For Project Managers

- Reference specific milestones or deadlines
- Clarify priority and importance
- Specify stakeholder requirements
- Include relevant metrics or KPIs

---

*Note: This guide should evolve based on the team's experience with Claude. Add new prompt templates as useful patterns emerge.* 