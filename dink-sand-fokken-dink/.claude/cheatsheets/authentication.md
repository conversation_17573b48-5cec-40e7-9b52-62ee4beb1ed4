# Authentication System Cheat Sheet

## Overview

The authentication system handles user authentication, authorization, and session management. It uses JWT tokens for stateless authentication and supports role-based access control.

## Key Components

- **AuthService**: Core service for authentication operations
- **TokenUtils**: Utilities for JWT token generation and validation
- **AuthMiddleware**: Express middleware for protecting routes
- **UserRepository**: Data access for user authentication information
- **RoleService**: Service for managing user roles and permissions

## Common Operations

### User Authentication

```javascript
// Login a user
async function loginUser(email, password) {
  const authService = require('src/auth/services/authService');
  const { token, user } = await authService.login(email, password);
  return { token, user };
}

// Verify a token
async function verifyToken(token) {
  const tokenUtils = require('src/auth/utils/tokenUtils');
  const payload = await tokenUtils.verifyToken(token);
  return payload;
}
```

### Protecting Routes

```javascript
// Protect a route with authentication middleware
const express = require('express');
const { authenticate } = require('src/api/middleware/authenticate');
const router = express.Router();

// Public route
router.get('/public', (req, res) => {
  res.json({ message: 'Public route' });
});

// Protected route (requires authentication)
router.get('/protected', authenticate(), (req, res) => {
  // req.user contains the authenticated user
  res.json({ message: `Hello ${req.user.name}` });
});

// Role-based protected route
router.get('/admin', authenticate({ roles: ['admin'] }), (req, res) => {
  res.json({ message: 'Admin route' });
});
```

### User Registration

```javascript
// Register a new user
async function registerUser(userData) {
  const authService = require('src/auth/services/authService');
  const user = await authService.register(userData);
  return user;
}
```

### Password Management

```javascript
// Change password
async function changePassword(userId, currentPassword, newPassword) {
  const authService = require('src/auth/services/authService');
  await authService.changePassword(userId, currentPassword, newPassword);
}

// Request password reset
async function requestPasswordReset(email) {
  const authService = require('src/auth/services/authService');
  const resetToken = await authService.requestPasswordReset(email);
  return resetToken;
}

// Reset password with token
async function resetPassword(resetToken, newPassword) {
  const authService = require('src/auth/services/authService');
  await authService.resetPassword(resetToken, newPassword);
}
```

## Configuration Options

| Option | Description | Default | Environment Variable |
|--------|-------------|---------|---------------------|
| `tokenSecret` | Secret key for JWT signing | N/A (Required) | `AUTH_TOKEN_SECRET` |
| `tokenExpiry` | Token expiration time | `'1d'` | `AUTH_TOKEN_EXPIRY` |
| `refreshTokenExpiry` | Refresh token expiration | `'7d'` | `AUTH_REFRESH_TOKEN_EXPIRY` |
| `passwordHashRounds` | Bcrypt hash rounds | `12` | `AUTH_PASSWORD_HASH_ROUNDS` |
| `resetTokenExpiry` | Password reset token expiry | `'1h'` | `AUTH_RESET_TOKEN_EXPIRY` |

## Common Pitfalls and Gotchas

### Token Expiration

- JWT tokens use seconds for expiration timestamps, not milliseconds
- Always convert `Date.now()` to seconds when comparing with token expiry
- Token expiry is based on UTC time, not local time

```javascript
// CORRECT way to check token expiry
const isExpired = expiryTimestamp < Math.floor(Date.now() / 1000);

// INCORRECT way (will cause premature expiration)
const isExpired = expiryTimestamp < Date.now(); // Wrong! Different units
```

### Password Hashing

- Never store plain-text passwords
- Always use bcrypt or similar for password hashing
- Password comparison should use timing-safe comparison functions

```javascript
// CORRECT way to compare passwords
const bcrypt = require('bcrypt');
const isMatch = await bcrypt.compare(plainTextPassword, hashedPassword);

// INCORRECT way (vulnerable to timing attacks)
const isMatch = hashPassword(plainTextPassword) === hashedPassword; // Wrong!
```

### CSRF Protection

- JWT in cookies needs CSRF protection
- Use the double-submit cookie pattern or CSRF tokens
- Set appropriate cookie flags (HttpOnly, Secure, SameSite)

```javascript
// CORRECT way to set auth cookies
res.cookie('token', token, {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict',
  maxAge: tokenExpiryInMilliseconds
});

// Also send CSRF token in response body
res.json({ 
  user,
  csrfToken: generateCsrfToken(user.id)
});
```

### Role-Based Access Control

- Check roles after verifying token validity
- Use specific role checks rather than comparing arrays
- Consider using permission-based checks for finer control

```javascript
// CORRECT way to check roles
function hasRole(user, requiredRole) {
  return user.roles.includes(requiredRole);
}

// BETTER approach with permissions
function hasPermission(user, requiredPermission) {
  return user.permissions.includes(requiredPermission);
}
```

## Debugging Tips

### Token Validation Issues

1. Check token expiration (remember seconds vs milliseconds)
2. Verify the token signature with the correct secret
3. Ensure all required claims are present in the token

### Authentication Failures

1. Check user credentials in the database
2. Verify password hashing configuration
3. Look for account status flags (locked, disabled, etc.)

### Authorization Issues

1. Verify the user has the required roles/permissions
2. Check role assignment in the database
3. Ensure role middleware is correctly configured

## Performance Considerations

- Token validation is CPU-intensive due to cryptographic operations
- Consider caching user permissions to reduce database lookups
- For high-traffic applications, consider using a token blacklist with Redis

## Security Best Practices

1. Use HTTPS for all authentication requests
2. Implement rate limiting for login attempts
3. Use secure password policies (minimum length, complexity)
4. Set appropriate token expiration times
5. Implement account lockout after failed attempts
6. Use security headers (HSTS, Content-Security-Policy)
7. Regularly rotate JWT signing keys 