version: '3.8'

services:
  app:
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile
    container_name: ${PROJECT_NAME:-app}-app
    restart: unless-stopped
    volumes:
      - ../:/app
      - /app/node_modules
      - /app/.venv
    environment:
      - DB_HOST=db
      - DB_PORT=5432
      - DB_NAME=${DB_NAME:-app}
      - DB_USER=${DB_USER:-postgres}
      - DB_PASSWORD=${DB_PASSWORD:-postgres}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - APP_SECRET_KEY=${APP_SECRET_KEY:-development_secret_key}
      - APP_DEBUG=${APP_DEBUG:-true}
    ports:
      - "${APP_PORT:-8000}:8000"
    depends_on:
      - db
      - redis
    networks:
      - app-network

  db:
    image: postgres:14-alpine
    container_name: ${PROJECT_NAME:-app}-db
    restart: unless-stopped
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_USER=${DB_USER:-postgres}
      - POSTGRES_PASSWORD=${DB_PASSWORD:-postgres}
      - POSTGRES_DB=${DB_NAME:-app}
    ports:
      - "${DB_PORT:-5432}:5432"
    networks:
      - app-network

  redis:
    image: redis:alpine
    container_name: ${PROJECT_NAME:-app}-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    container_name: ${PROJECT_NAME:-app}-nginx
    restart: unless-stopped
    ports:
      - "${NGINX_PORT:-80}:80"
    volumes:
      - ../:/app
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - app
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data: 