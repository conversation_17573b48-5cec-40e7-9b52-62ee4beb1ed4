# Generic Project Template

A project template with standardized structure and configurations for rapid project setup.

## Overview

This is a generic project template that includes standard configurations for Claude AI assistance, Docker containerization, development workflows, and project management. It's designed to accelerate project setup and ensure consistent structure across projects.

## Features

- Pre-configured Claude AI assistance with project status templates
- Docker wrapper with standardized scripts and configurations
- Cursor IDE rules and configuration
- Project management documentation (changelog, roadmap)
- Standardized directory structure
- AI-driven feature development with roadmap tracking
- Git feature branch workflow with best practices
- Nix shell for consistent development environments

## Development Environment

This project requires using the Nix shell for development to ensure consistent environments across all developers.

### Prerequisites

- [<PERSON>](https://nixos.org/download.html) package manager installed
- [Cursor IDE](https://cursor.sh/) is recommended (configured to use Nix shell automatically)

### Getting Started

1. Install Nix:
   ```bash
   curl -L https://nixos.org/nix/install | sh
   ```

2. Clone the repository:
   ```bash
   git clone [repository-url]
   cd [project-name]
   ```

3. Enter the Nix shell:
   ```bash
   nix-shell
   ```

4. Start developing with consistent tooling!

## Claude-Specific Optimizations

This project includes specialized optimizations to enhance <PERSON>'s ability to work with the codebase:

1. **Metadata Directory Structure**
   - Component dependency graphs in machine-readable format
   - File classification metadata (implementation vs interface vs test)
   - Error patterns database with solutions

2. **Semantic Code Indexing**
   - Function-to-function call graphs
   - Type relationships and interface implementations
   - Intent classification for code sections

3. **Debug History Database**
   - Logging of debugging sessions with error solution pairs
   - Categorization by component and error type
   - Context and code versions for each fix

4. **Pattern Libraries**
   - Canonical implementation patterns
   - Error handling patterns with context preservation
   - Composition patterns for reliability

5. **Component-Specific Cheat Sheets**
   - Quick-reference guides for common operations
   - Known pitfalls and edge cases
   - Component-specific "gotchas"

6. **Queries-and-Answers Database**
   - Previous solved problems indexed by component
   - Context from the fix process
   - Reasoning used to solve each problem

7. **Model-Friendly Documentation**
   - Explicit sections for purpose, schema, patterns, interfaces, invariants, and error states
   - Structured for optimal AI comprehension

8. **Delta Summaries**
   - Semantic change logs between versions
   - Focus on API changes and their implications
   - Reasoning behind significant changes

9. **Memory Anchors**
   - Special comments with UUIDs for precise reference
   - Semantic structure for ease of reference

These optimizations create a Claude-optimized layer on top of the standard repository structure, allowing both human developers and Claude to work more efficiently with this codebase.

## Project Structure

```
project/
├── .claude/                  # Claude AI assistant configuration and project status
│   ├── cheatsheets/          # Quick reference guides
│   ├── code_index/           # Code navigation helpers
│   ├── daily_summaries/      # Daily progress summaries
│   ├── debug_history/        # Debugging session history
│   ├── delta/                # Semantic change logs
│   ├── feature_plans/        # Feature development plans
│   ├── metadata/             # Project metadata
│   ├── patterns/             # Common code patterns
│   ├── qa/                   # Queries and answers database
│   ├── status_reports/       # Project status reports
│   ├── architecture.md       # System architecture documentation
│   ├── roadmap.md            # Development roadmap
│   └── ... other docs        # Various documentation files
├── .cursor/                  # Cursor IDE configuration
├── .dockerwrapper/           # Docker configuration
├── docs/                     # Project documentation
├── src/                      # Source code
├── tests/                    # Test suite
├── CHANGELOG.md              # Project changelog
└── README.md                 # Project README
```

## Getting Started

1. Clone this repository
2. Rename the project directory and update project-specific information
3. Initialize git repository: `git init`
4. Review and modify the documentation in `.claude/` to match your project
5. Update Docker configurations in `.dockerwrapper/` as needed
6. Begin development!

## AI-Driven Development

This template includes comprehensive guidance for AI-assisted development:

1. **Roadmap-Driven Development**: The AI follows the roadmap in `.claude/roadmap.md` to prioritize features.
2. **Feature Branch Workflow**: The AI implements features following Git best practices outlined in `.claude/feature_branch_workflow.md`.
3. **Feature Planning**: Each feature has a detailed plan in `.claude/feature_plans/`.
4. **Documentation Standards**: All documentation uses Markdown with Mermaid diagrams for visualizations.

## Documentation Standards

- All documentation should be written in Markdown
- Use Mermaid diagrams for all visual representations (flowcharts, sequence diagrams, etc.)
- Keep documentation up to date with code changes
- Document APIs, database schemas, and architectural decisions

Example of a Mermaid diagram:

```mermaid
graph TD
    A[Start] --> B{Is it working?}
    B -->|Yes| C[Great!]
    B -->|No| D[Debug]
    D --> B
```

## Documentation Locations

Detailed documentation is available in the following locations:

- `docs/` - General project documentation
- `.claude/` - Project status and technical documentation
- `.dockerwrapper/README.md` - Docker setup and usage
- `.claude/documentation_standards.md` - Detailed documentation guidelines

## License

[Add appropriate license information here] 