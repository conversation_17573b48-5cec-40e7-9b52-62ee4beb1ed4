---
id: QA-security-report-001
created: 2025-03-15
authors:
  - name: "<PERSON>"
    email: "syd<PERSON>@example.com"
    role: "QA Engineer"
  - name: "<PERSON>"
    email: "<EMAIL>"
    role: "Security Analyst"
tested:
  - ARCH-auth-feature-001
  - PRD-login-feature-001
  - DEV-auth-implementation-001
test_date: 2025-03-22
---

# Security Validation Report <!-- TEST-001 -->

This is an example QA report using the new ID naming convention.

## Test Results <!-- TEST-002 -->

| Requirement ID | Status | Notes |
|---------------|--------|-------|
| ARCH-auth-feature-001:REQ-001 | PASS | OAuth 2.0 implementation verified |
| PRD-login-feature-001:STORY-001 | PASS | Login functionality works as expected |

## Security Issues <!-- BUG-001 -->

No security issues found.