---
id: PRD-login-feature-001
created: 2025-03-15
authors:
  - name: "<PERSON>"
    email: "jord<PERSON>@example.com"
    role: "Product Manager"
  - name: "<PERSON>"
    email: "<EMAIL>"
    role: "UX Designer"
stakeholders:
  - name: "<PERSON>"
    department: "Marketing"
---

# Login Feature Requirements <!-- STORY-001 -->

This is an example product requirements document using the new ID naming convention.

## User Stories <!-- STORY-002 -->

- As a user, I want to log in with my email and password
- As a user, I want to reset my password if I forget it

## Acceptance Criteria <!-- TEST-001 -->

- User can successfully log in
- System validates input and shows appropriate error messages