---
id: ARCH-auth-feature-001
created: 2025-03-15
authors:
  - name: "<PERSON>"
    email: "<EMAIL>"
    role: "Solution Architect"
  - name: "<PERSON>"
    email: "<EMAIL>"
    role: "Security Architect"
---

# Authentication Feature Design <!-- SPEC-001 -->

This is an example architecture document using the new ID naming convention.

## Requirements <!-- REQ-001 -->

- Must support OAuth 2.0
- Must implement role-based access control

## Technical Design <!-- SPEC-002 -->

Detailed technical design for the authentication system.