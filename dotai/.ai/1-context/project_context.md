# Project Context Document

Type of project: quick prototype, semi-production code, full production code

## 1. Project Overview
- **Project Name**: 
- **Domain**: 
- **Core Purpose**: 

## 2. Key Objectives
- Primary business goals
- Success metrics
- Timeline milestones

## 3. Stakeholder Map
| Role | Responsibilities | Communication Channels |
|------|------------------|-------------------------|
|      |                  |                         |

## 4. Technical Architecture
- System components diagram
- Technology stack
- Integration points

## 5. Key Constraints
- Regulatory requirements
- Technical limitations
- Resource boundaries

## 6. Risk Register
| Risk Category | Potential Impact | Mitigation Strategy |
|---------------|------------------|---------------------|
|               |                  |                     |

## 7. Glossary
- Domain-specific terms
- Technical acronyms
- Business jargon

## 8. Decision Log
| Date | Decision | Rationale | Owner |
|------|----------|-----------|-------|
|      |          |           |       |