# Project Conventions

## File and Directory Naming
- Use lowercase letters and hyphens for file and directory names
- Avoid spaces and special characters
- Use descriptive, purpose-indicating names
- Keep names concise but meaningful

## Project Structure
- `/src` - Source code
- `/docs` - Documentation
- `/tests` - Test files
- `/scripts` - Build and automation scripts
- `/config` - Configuration files
- `/.ai` - AI-related documentation and specifications
  - `/1-context` - Project context and conventions
  - `/2-technical-design` - Technical specifications and requirements
  - `/3-implementation` - Implementation details and guides

## Code Style
- Follow language-specific style guides
- Use consistent indentation (2 spaces for most languages)
- Include meaningful comments and documentation
- Implement clear error handling

## Version Control
- Use semantic versioning (MAJOR.MINOR.PATCH)
- Write descriptive commit messages
- Branch naming:
  - `feature/` for new features
  - `bugfix/` for bug fixes
  - `hotfix/` for urgent fixes
  - `release/` for release branches

## Documentation
- Maintain up-to-date README files
- Document API endpoints and interfaces
- Include setup and deployment instructions
- Keep change logs current

## Testing
- Write unit tests for new features
- Maintain integration tests
- Document test cases and scenarios
- Include performance benchmarks

## Security
- Follow security best practices
- Document security requirements
- Regular security audits
- Implement access controls

## Deployment
- Define deployment environments
- Document deployment procedures
- Include rollback procedures
- Monitor deployment metrics
