# .ai Directory Structure

This document provides an overview of the `.ai` directory structure, which combines the structured organization of the `.ai` pattern with the rich content and features of the `.claude` pattern.

## Overview

The `.ai` directory is organized to optimize AI-assisted development workflows and provide comprehensive documentation and guidance for both human developers and AI assistants.

```
.ai/
├── README.md                   # Overview of the .ai directory
├── STRUCTURE.md                # This document, explaining the structure
└── docs/
    ├── 0-ai-config/            # AI tool configurations
    │   ├── claude/             # Claude-specific configuration
    │   ├── cursor/             # Cursor IDE configuration
    │   └── cline/              # Command-line AI assistant configs
    ├── 1-context/              # High-level project documentation
    │   ├── standards/          # Documentation standards
    │   └── project/            # Project context and goals
    ├── 2-technical-design/     # Requirements and specifications
    │   ├── development_workflow/ # Development process documentation
    │   ├── requirements/       # Project requirements
    │   └── features/           # Feature specifications
    │       └── _template/      # Standardized feature template
    ├── 3-development/          # Development documentation
    │   ├── code_index/         # Code navigation helpers
    │   ├── patterns/           # Common code patterns
    │   ├── cheatsheets/        # Quick reference guides
    │   ├── support/            # Debug and troubleshooting resources
    │   └── lifecycle/          # Development lifecycle tracking
    │       ├── roadmap/        # Development roadmap
    │       ├── sprints/        # Sprint planning and retrospectives
    │       ├── daily/          # Daily progress summaries
    │       └── deltas/         # Version change documentation
    └── 4-acceptance/           # Validation and compliance
        ├── security/           # Security compliance
        ├── performance/        # Performance validation
        └── code_quality/       # Code quality validation
```

## Directory Purposes

### 0-ai-config/

Configuration for AI tools used in the development workflow:

- **claude/**: Claude AI assistant configuration, including prompting guidelines
- **cursor/**: Cursor IDE configuration, including rules for AI-assisted coding
- **cline/**: Command-line AI assistant configuration

### 1-context/

High-level project documentation that provides essential context:

- **standards/**: Documentation and coding standards for the project
- **project/**: Project overview, architecture, goals, and key decisions

### 2-technical-design/

Technical specifications and requirements:

- **development_workflow/**: Documentation of development processes
- **requirements/**: Project requirements documentation
- **features/**: Detailed specifications for individual features
  - **_template/**: Standardized template for feature documentation

### 3-development/

Resources to assist during the development process:

- **code_index/**: Indexes of code relationships and patterns
- **patterns/**: Reusable implementation patterns
- **cheatsheets/**: Quick reference guides for common tasks
- **support/**: Debugging and troubleshooting resources
- **lifecycle/**: Tracking of the development lifecycle
  - **roadmap/**: Future development plans
  - **sprints/**: Sprint planning and tracking
  - **daily/**: Daily development summaries
  - **deltas/**: Documentation of significant changes

### 4-acceptance/

Validation criteria and compliance documentation:

- **security/**: Security requirements and validation
- **performance/**: Performance requirements and testing
- **code_quality/**: Code quality standards and validation

## Key Files

This structure includes several key files:

- **.ai/README.md**: Overview of the AI-assisted development approach
- **.ai/docs/0-ai-config/cursor/.cursorrules**: Guidance for Cursor AI
- **.ai/docs/1-context/standards/documentation_standards.md**: Standards for documentation
- **.ai/docs/2-technical-design/features/_template/specification.md**: Template for feature specs
- **.ai/docs/3-development/patterns/error_handling.md**: Standard error handling patterns
- **.ai/docs/4-acceptance/security/checklist.md**: Security validation checklist

## Backward Compatibility

For backward compatibility with existing `.claude`-based workflows:

- A symbolic link has been created from `.claude/docs` to `.ai/docs`
- This allows tools and workflows expecting the `.claude` directory structure to work with the new `.ai` directory

## Integration with AI Tools

This structure is designed for optimal integration with:

1. **Claude**: Through explicit documentation and context organization
2. **Cursor**: Through `.cursorrules` and structured documentation
3. **Cline**: Through command-line AI configuration
4. **Other AI Tools**: Through the standardized documentation structure

## Extensibility

The `.ai` directory is designed to be extensible:

1. New AI tools can be added in `0-ai-config/`
2. Additional documentation sections can be added within the four main categories
3. The structure accommodates both small projects and large, complex systems

## Transition Guide

If you are transitioning from `.claude` to `.ai`:

1. Continue maintaining key documentation in the `.ai` structure
2. The symbolic link ensures backward compatibility
3. Gradually migrate workflows to reference the `.ai` directory directly
4. Update CI/CD and development scripts to use the new structure

## Best Practices

1. Keep documentation up-to-date as the code evolves
2. Use the standardized templates for consistency
3. Reference specific paths when working with AI assistants
4. Maintain a balance between comprehensive documentation and maintainability 