# AI-Assisted Development Documentation

@context-card: {
  "type": "overview",
  "purpose": "AI-assisted development optimization",
  "keyFiles": [
    "docs/0-ai-config/TOKEN_OPTIMIZATION.md",
    "docs/0-ai-config/SHORTHAND.md",
    "STRUCTURE.md"
  ]
}

## Purpose

- Structured context for AI assistants
- Standardized workflow documentation
- Human-AI collaboration optimization
- Token-efficient knowledge organization

## Directory Structure

```
.ai/
└── docs/
    ├── 0-ai-config/            # AI-CFG: Tool configs
    ├── 1-context/              # CTX: Project context
    ├── 2-technical-design/     # TECH: Specifications
    ├── 3-development/          # DEV: Implementation
    └── 4-acceptance/           # ACC: Validation
```

## Key Features

1. **Multi-Tool Support**: Claude, Cursor, Cline configurations
2. **Token Optimization**: Index files, context cards, shorthands
3. **Pattern Libraries**: Standardized implementation patterns [EH-1, API-2, etc.]
4. **Semantic Paths**: Self-documenting directory structure
5. **Tiered Documentation**: Core principles ➝ Implementation ➝ Examples

## Getting Started

1. Reference patterns using shorthands: `[EH-1]` = Error Handling Pattern 1
2. Use context hints in code comments: `@ai-context: [PATTERN] EH-1`
3. Follow template formats: see `AI-CFG/claude/prompt_templates.md`
4. Look for index files (INDEX.md) for quick overviews
5. Check `TOKEN_OPTIMIZATION.md` for efficiency best practices

## Integration

```typescript
/**
 * @ai-context: [PATTERN] EH-1 "Try-Catch with Context pattern"
 * @ai-context: [DOC] ACC/security/tiered/core/authentication.md
 */
function secureOperation() {
  // Implementation follows [EH-1]
}
```

## References

- Pattern Index: `DEV/patterns/INDEX.md`
- Shorthand Reference: `AI-CFG/SHORTHAND.md`
- Optimization Guide: `AI-CFG/TOKEN_OPTIMIZATION.md`
- Structure Details: `STRUCTURE.md` 