# Authentication: Core Principles

*Security Domain: Authentication*  
*Category: Core Principles*  
*PatternID: [SEC-AUTH-CORE]*

## Essential Principles

1. **Defense in Depth**
   - No single point of authentication failure
   - Multiple layers of security controls

2. **Least Privilege**
   - Grant minimal access rights
   - Time-limited privileges where possible

3. **Secure Defaults**
   - Authentication required by default
   - Secure default configurations

4. **Strong Credential Protection**
   - Never store plaintext passwords
   - Use proven password hashing algorithms

5. **Fail Securely**
   - Deny access on authentication failure
   - Don't reveal sensitive information in errors

## Required Properties

| Property | Requirement |
|----------|-------------|
| Password Hashing | Must use bcrypt, Argon2, or PBKDF2 |
| MFA Support | Must support at least SMS and authenticator apps |
| Session Management | HTTP-only, secure cookies with appropriate expiration |
| Account Lockout | Must implement after multiple failed attempts |
| Credential Storage | No plaintext storage, uses salted hashes |

## Critical Anti-Patterns to Avoid

- ❌ Plaintext password storage
- ❌ Client-side authentication only
- ❌ Hard-coded credentials
- ❌ Persistent session tokens without expiry
- ❌ Predictable session identifiers

## References

- See implementation details: `ACC/security/tiered/implementation/authentication.md`
- See examples: `ACC/security/tiered/examples/authentication.md`
- Related: [SEC-1] Authentication Gateway 