# Authentication: Implementation Guidelines

*Security Domain: Authentication*  
*Category: Implementation Details*  
*PatternID: [SEC-AUTH-IMPL]*  
*Core Principles: [SEC-AUTH-CORE]*

## Password Storage Implementation

```typescript
import * as argon2 from 'argon2';

class PasswordService {
  // Hash a password using Argon2id
  async hashPassword(password: string): Promise<string> {
    return await argon2.hash(password, {
      type: argon2.argon2id,
      memoryCost: 2**16,  // 65536 KiB
      timeCost: 3,        // 3 iterations
      parallelism: 1,     // 1 thread
      saltLength: 16      // 16 bytes
    });
  }

  // Verify a password against a hash
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    return await argon2.verify(hash, password);
  }
}
```

## Session Management Implementation

```typescript
import { v4 as uuidv4 } from 'uuid';
import { Redis } from 'ioredis';

class SessionManager {
  private redis: Redis;
  private readonly SESSION_EXPIRY_SECONDS = 3600; // 1 hour

  constructor(redisClient: Redis) {
    this.redis = redisClient;
  }

  // Create a new session
  async createSession(userId: string, metadata: object = {}): Promise<string> {
    const sessionId = uuidv4();
    const sessionData = {
      userId,
      created: Date.now(),
      lastActive: Date.now(),
      ...metadata
    };

    await this.redis.setex(
      `session:${sessionId}`, 
      this.SESSION_EXPIRY_SECONDS, 
      JSON.stringify(sessionData)
    );

    return sessionId;
  }

  // Get session data
  async getSession(sessionId: string): Promise<any | null> {
    const data = await this.redis.get(`session:${sessionId}`);
    if (!data) return null;

    const session = JSON.parse(data);
    
    // Update last active time
    session.lastActive = Date.now();
    await this.redis.setex(
      `session:${sessionId}`, 
      this.SESSION_EXPIRY_SECONDS, 
      JSON.stringify(session)
    );

    return session;
  }

  // Invalidate a session
  async invalidateSession(sessionId: string): Promise<boolean> {
    const result = await this.redis.del(`session:${sessionId}`);
    return result === 1;
  }
}
```

## Account Lockout Implementation

```typescript
class AccountLockoutService {
  private redis: Redis;
  private readonly MAX_FAILED_ATTEMPTS = 5;
  private readonly LOCKOUT_DURATION_SECONDS = 900; // 15 minutes

  constructor(redisClient: Redis) {
    this.redis = redisClient;
  }

  // Record a failed login attempt
  async recordFailedAttempt(username: string): Promise<number> {
    const key = `auth:failed:${username}`;
    const attempts = await this.redis.incr(key);
    
    // Set expiry if it's a new key
    if (attempts === 1) {
      await this.redis.expire(key, this.LOCKOUT_DURATION_SECONDS);
    }
    
    return attempts;
  }

  // Check if an account is locked
  async isAccountLocked(username: string): Promise<boolean> {
    const key = `auth:failed:${username}`;
    const attempts = await this.redis.get(key);
    return attempts !== null && parseInt(attempts) >= this.MAX_FAILED_ATTEMPTS;
  }

  // Reset failed attempts after successful login
  async resetFailedAttempts(username: string): Promise<void> {
    await this.redis.del(`auth:failed:${username}`);
  }
}
```

## References

- Core principles: `ACC/security/tiered/core/authentication.md`
- Examples: `ACC/security/tiered/examples/authentication.md`
- Related: [SEC-1] Authentication Gateway 