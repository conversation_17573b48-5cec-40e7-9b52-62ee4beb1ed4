# Project Context

This directory contains high-level documentation that provides context about the project's purpose, goals, and overall architecture.

## Purpose

Project context documentation helps both developers and AI assistants understand:

1. What the project is trying to achieve
2. Who the target users are
3. What problem the project solves
4. How the various components work together
5. What design decisions have been made and why

## Key Documents

The following documents should be maintained in this directory:

### project_overview.md

A comprehensive overview of the project, including:
- Mission statement and goals
- Target audience
- Key features and capabilities
- Business value and use cases
- Project history and roadmap
- Team structure and responsibilities

### architecture_overview.md

A high-level description of the system architecture:
- Major components and their relationships
- Technology stack choices and justifications
- System boundaries and external integrations
- Data flow diagrams
- Deployment architecture
- Performance and scaling considerations

### glossary.md

A glossary of terms specific to the project domain:
- Business terminology
- Technical terms
- Acronyms and abbreviations
- Standard naming conventions

### key_decisions.md

A record of important architectural and design decisions:
- What decision was made
- When it was made
- Alternatives that were considered
- Justification for the chosen approach
- Implications and constraints
- Future considerations

## Best Practices

When maintaining project context documents:

1. **Keep them up to date**: Update as the project evolves
2. **Be concise**: Focus on what's important for understanding the system
3. **Use visuals**: Include diagrams where appropriate
4. **Cross-reference**: Link to more detailed documentation
5. **Use consistent terminology**: Align with terms in the glossary
6. **Focus on why, not just what**: Explain reasoning behind decisions
7. **Consider the audience**: Write for both technical and non-technical readers

## Working with AI Assistants

Project context is especially important for AI assistance. When working with AI:

1. Reference these documents to provide context for complex requests
2. Point to specific sections that are relevant to the current task
3. Ask AI to incorporate project context when generating code or documentation
4. Have AI help keep these documents updated as the project evolves

Example prompt:
```
Based on the architecture described in `.ai/docs/1-context/project/architecture_overview.md`, 
help me design a new component that will handle user notifications.
``` 