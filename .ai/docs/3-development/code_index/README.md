# Code Index

This directory contains various code indexing artifacts that help both human developers and AI assistants understand the codebase structure, relationships, and patterns.

## Purpose

Code indexing provides a machine-readable representation of the codebase, making it easier to:

1. Navigate complex code relationships
2. Understand dependencies between components
3. Identify implementation patterns
4. Trace function call flows
5. Discover type relationships

## Index Types

### Function Call Graph

`function_call_graph.json` maps which functions call other functions, allowing you to trace execution flows through the codebase.

```json
{
  "functionName": {
    "calls": ["otherFunction1", "otherFunction2"],
    "calledBy": ["parentFunction1", "parentFunction2"],
    "location": "path/to/file.ext",
    "lineNumber": 42
  }
}
```

### Type Relationships

`type_relationships.json` documents how types relate to each other (inheritance, implementation, composition).

```json
{
  "typeName": {
    "extends": ["parentType"],
    "implements": ["interface1", "interface2"],
    "composedOf": [
      { "name": "field1", "type": "type1" },
      { "name": "field2", "type": "type2" }
    ],
    "location": "path/to/file.ext",
    "lineNumber": 42
  }
}
```

### Intent Classification

`intent_classification.json` categorizes code sections by their intended purpose.

```json
{
  "path/to/file.ext:12-45": {
    "intent": "AUTHENTICATION",
    "description": "JWT token validation middleware",
    "components": ["auth", "middleware"],
    "patterns": ["validation", "middleware"]
  }
}
```

### File Classification

`file_classification.json` categorizes files by their role in the codebase.

```json
{
  "path/to/file.ext": {
    "type": "IMPLEMENTATION|INTERFACE|TEST|CONFIG",
    "component": "componentName",
    "description": "Brief description of file purpose" 
  }
}
```

## Generating Code Indexes

The code indexes are typically generated through:

1. Static code analysis tools
2. Custom scripts that parse the codebase
3. AI-assisted analysis of code structures
4. Developer annotations during development

## Usage Guidelines

- Consult these indexes when working on unfamiliar parts of the codebase
- Update indexes when making significant structural changes
- Reference specific index entries when discussing code with AI assistants
- Use indexes to help identify patterns and conventions 