# Context Hints for Code

This document explains how to use context hints in code comments to reference documentation without duplicating content, reducing token usage while maintaining AI context.

## What Are Context Hints?

Context hints are special code comments that reference specific documentation or patterns. They help:

1. Reduce token usage by avoiding duplicate explanations
2. Maintain consistent implementation patterns
3. Reference documentation without copying it
4. Provide AI assistants with navigation hints to relevant information

## Context Hint Format

```
// @ai-context: [HINT_TYPE] PATH_OR_PATTERN_ID "Optional description"
```

Examples:
```typescript
// @ai-context: [PATTERN] EH-1 "Try-Catch with Context pattern"
// @ai-context: [DOC] ACC/security/tiered/core/authentication.md
// @ai-context: [STANDARD] DB-1 "Repository Pattern implementation"
```

## Context Hint Types

| Type | Description | Example |
|------|-------------|---------|
| PATTERN | References an implementation pattern | `[PATTERN] EH-1` |
| DOC | References a documentation file | `[DOC] DEV/patterns/INDEX.md` |
| STANDARD | References a coding standard | `[STANDARD] SEC-AUTH-CORE` |
| RULE | References a specific rule | `[RULE] SEC-5.3` |
| CONTEXT | Provides general context | `[CONTEXT] Authentication flow` |

## How to Use Context Hints

### In Class Definitions

```typescript
/**
 * Handles user authentication and session management
 * 
 * @ai-context: [PATTERN] SEC-1 "Authentication Gateway"
 * @ai-context: [DOC] ACC/security/tiered/core/authentication.md
 */
class AuthenticationService {
  // Implementation...
}
```

### In Method Implementations

```typescript
/**
 * Validates user credentials against the database
 * 
 * @ai-context: [PATTERN] EH-1 "Try-Catch with Context"
 * @ai-context: [STANDARD] SEC-AUTH-IMPL "Password validation"
 */
async validateCredentials(username: string, password: string) {
  // Implementation...
}
```

### In Function Definitions

```typescript
/**
 * Process and validate user input for the registration form
 * 
 * @ai-context: [PATTERN] API-3 "Input Validation Pipeline"
 * @ai-context: [PATTERN] EH-2 "Result Type Pattern"
 */
function validateRegistrationInput(input: RegistrationInput): ValidationResult {
  // Implementation...
}
```

### In Configuration Files

```javascript
// @ai-context: [DOC] AI-CFG/cursor/config.md "Cursor configuration"
module.exports = {
  // Configuration...
}
```

## Benefits for AI Assistants

When AI assistants see context hints, they can:

1. Look up the referenced documentation for details
2. Understand the implementation pattern being used
3. Adhere to referenced standards
4. Provide more consistent and accurate assistance
5. Save tokens by not requiring full explanation in the prompt

## Best Practices

1. **Be Specific**: Reference specific patterns or documents rather than general areas
2. **Don't Overuse**: Add hints only where important for understanding or implementation
3. **Keep Updated**: Update hints when referenced patterns change
4. **Use in Key Places**: Focus on important methods, classes, and functions
5. **Maintain Consistency**: Use consistent hint formats throughout the codebase

## Examples by Language

### TypeScript/JavaScript

```typescript
// @ai-context: [PATTERN] UI-1 "Container/Presentation Pattern"
```

### Python

```python
# @ai-context: [PATTERN] API-2 "Controller-Service-Repository"
```

### C#

```csharp
// @ai-context: [STANDARD] EH-1 "Try-Catch with Context"
```

### Java

```java
// @ai-context: [PATTERN] DB-1 "Repository Pattern"
```

### Go

```go
// @ai-context: [DOC] DEV/patterns/error_handling/EH-2.md
``` 