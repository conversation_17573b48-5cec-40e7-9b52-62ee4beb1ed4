# Error Handling Patterns

This document outlines the standard error handling patterns to be used throughout the codebase to ensure consistent, secure, and maintainable error management.

## Core Principles

1. **Explicit Error Types**: Use strongly-typed errors rather than generic exceptions
2. **Context Preservation**: Include contextual information with errors
3. **Appropriate Logging**: Log errors with the right severity level
4. **Security Awareness**: Prevent sensitive information exposure
5. **User-Friendly Messages**: Provide clear user-facing error messages
6. **Assertions**: Use assertions to validate assumptions and catch errors early
7. **Graceful Degradation**: Systems should fail gracefully when possible

## Common Error Handling Patterns

### Pattern 1: Try-Catch with Context

```typescript
try {
  const userData = await userService.getUserData(userId);
  return processUserData(userData);
} catch (error) {
  // Add context to the error
  const contextualError = new ApplicationError({
    message: "Failed to process user data",
    cause: error,
    context: { userId },
    code: ErrorCodes.USER_DATA_PROCESSING_FAILED
  });
  
  // Log the error with full context for debugging
  logger.error(contextualError);
  
  // Return user-friendly error
  throw new UserFacingError(
    "We couldn't process your information. Please try again later.",
    contextualError
  );
}
```

### Pattern 2: Result Type Pattern

```typescript
// Define a Result type that can hold either success or failure
type Result<T, E = Error> = Success<T> | Failure<E>;

// Example usage
function divideNumbers(a: number, b: number): Result<number, DivisionError> {
  // Assert b is not zero to catch errors early
  assert(b !== 0, "Division by zero is not allowed");
  
  if (b === 0) {
    return new Failure(new DivisionError("Division by zero"));
  }
  return new Success(a / b);
}

// Using the result
const divisionResult = divideNumbers(10, 2);
if (divisionResult.isSuccess()) {
  const value = divisionResult.getValue();
  console.log(`Result: ${value}`);
} else {
  const error = divisionResult.getError();
  logger.error("Division failed", { error, context: { a: 10, b: 2 } });
}
```

### Pattern 3: Middleware Error Handling (Web)

```typescript
// Error handling middleware for Express
function errorHandlerMiddleware(
  error: Error, 
  request: Request, 
  response: Response, 
  next: NextFunction
): void {
  // Log the error with request context
  const requestContext = {
    path: request.path,
    method: request.method,
    ip: request.ip,
    userId: request.user?.id
  };
  
  logger.error("Request failed", { error, context: requestContext });
  
  // Determine appropriate response
  if (error instanceof ValidationError) {
    response.status(400).json({
      error: "Invalid input",
      details: error.getPublicDetails() // Only exposes safe information
    });
  } else if (error instanceof AuthorizationError) {
    response.status(403).json({
      error: "Not authorized"
    });
  } else {
    // Don't expose internal errors to clients
    const errorId = generateErrorId();
    logger.error("Unexpected error", { errorId, error });
    
    response.status(500).json({
      error: "An unexpected error occurred",
      errorId // For support reference
    });
  }
}
```

## Error Types

Define explicit error types rather than using generic errors:

```typescript
// Base application error
class ApplicationError extends Error {
  public readonly cause?: Error;
  public readonly context: Record<string, unknown>;
  public readonly code: string;
  
  constructor(options: {
    message: string;
    cause?: Error;
    context?: Record<string, unknown>;
    code: string;
  }) {
    super(options.message);
    this.cause = options.cause;
    this.context = options.context || {};
    this.code = options.code;
    Object.setPrototypeOf(this, ApplicationError.prototype);
  }
}

// Specific error types
class ValidationError extends ApplicationError {
  constructor(options: { message: string; fields: Record<string, string> }) {
    super({
      message: options.message,
      context: { fields: options.fields },
      code: "VALIDATION_ERROR"
    });
    Object.setPrototypeOf(this, ValidationError.prototype);
  }
  
  getPublicDetails(): Record<string, string> {
    return this.context.fields as Record<string, string>;
  }
}
```

## Logging Best Practices

When logging errors:

1. Include structured data rather than string concatenation
2. Use appropriate log levels (error, warn, info, debug)
3. Include relevant context but avoid sensitive data
4. Use correlation IDs to track related log entries
5. Log both the error and its cause chain

Example:

```typescript
try {
  await processPayment(paymentData);
} catch (error) {
  logger.error("Payment processing failed", {
    error,
    correlationId: requestId,
    paymentReference: paymentData.reference,
    // Don't include full payment details with card info
    paymentMethod: paymentData.method
  });
}
```

## Clean Handling of Expected Errors

For errors that are part of normal operation, use clean and explicit handling:

```typescript
async function getUserSettings(userId: string): Promise<UserSettings> {
  try {
    const userData = await db.users.findById(userId);
    if (!userData) {
      logger.info("User not found when fetching settings", { userId });
      return DEFAULT_USER_SETTINGS;
    }
    return userData.settings;
  } catch (error) {
    logger.error("Failed to fetch user settings", { error, userId });
    // Graceful degradation - return defaults rather than crash
    return DEFAULT_USER_SETTINGS;
  }
}
```

## API Error Responses

API errors should follow a consistent format:

```json
{
  "error": "Brief, user-friendly error message",
  "code": "ERROR_CODE_FOR_CLIENTS",
  "details": {
    "fieldName": "Specific error about this field",
    "otherField": "Another validation error"
  },
  "errorId": "unique-error-reference-id"
}
``` 