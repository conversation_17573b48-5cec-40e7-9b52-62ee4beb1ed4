# [EH-1] Try-Catch with Context

*Category: Error Handling*

## Purpose
Add contextual information to errors while preserving the original error details.

## Key Principles
- Preserve original error
- Add operation context
- Include relevant data
- Use consistent error types
- Provide appropriate logging

## Implementation

```typescript
try {
  const userData = await userService.getUserData(userId);
  return processUserData(userData);
} catch (error) {
  // Add context to the error
  const contextualError = new ApplicationError({
    message: "Failed to process user data",
    cause: error,
    context: { userId },
    code: ErrorCodes.USER_DATA_PROCESSING_FAILED
  });
  
  // Log the error with full context
  logger.error(contextualError);
  
  // Return user-friendly error
  throw new UserFacingError(
    "We couldn't process your information. Please try again later.",
    contextualError
  );
}
```

## When to Use
- Service boundaries
- External API calls
- Data processing operations
- User input handling

## Related Patterns
- [EH-2] Result Type Pattern
- [EH-4] Graceful Degradation
- [SEC-5] Audit Logging

## Cross-References
- SEC-4: Input Sanitization 