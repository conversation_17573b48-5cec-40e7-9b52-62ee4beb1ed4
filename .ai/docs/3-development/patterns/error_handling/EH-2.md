# [EH-2] Result Type Pattern

*Category: Error Handling*

## Purpose
Return explicit success/failure objects instead of throwing exceptions, making error flows explicit in the type system.

## Key Principles
- Make errors part of the return type
- Explicit error handling
- No unexpected exceptions
- Type safety throughout error flows
- Predictable error propagation

## Implementation

```typescript
// Define a Result type that can hold either success or failure
type Result<T, E = Error> = Success<T> | Failure<E>;

class Success<T> {
  readonly value: T;
  
  constructor(value: T) {
    this.value = value;
  }
  
  isSuccess(): this is Success<T> {
    return true;
  }
  
  isFailure(): this is never {
    return false;
  }
  
  getValue(): T {
    return this.value;
  }
}

class Failure<E> {
  readonly error: E;
  
  constructor(error: E) {
    this.error = error;
  }
  
  isSuccess(): this is never {
    return false;
  }
  
  isFailure(): this is Failure<E> {
    return true;
  }
  
  getError(): E {
    return this.error;
  }
}

// Example usage
function divideNumbers(a: number, b: number): Result<number, DivisionError> {
  if (b === 0) {
    return new Failure(new DivisionError("Division by zero"));
  }
  return new Success(a / b);
}

// Using the result
const divisionResult = divideNumbers(10, 2);
if (divisionResult.isSuccess()) {
  const value = divisionResult.getValue();
  console.log(`Result: ${value}`);
} else {
  const error = divisionResult.getError();
  logger.error("Division failed", { error });
}
```

## When to Use
- Functions with known failure cases
- Operations that might fail in expected ways
- APIs that need to return detailed error information
- When exceptions would be misused for control flow

## Related Patterns
- [EH-1] Try-Catch with Context
- [EH-4] Graceful Degradation
- [API-3] Input Validation Pipeline

## Cross-References
- API-2: Controller-Service-Repository 