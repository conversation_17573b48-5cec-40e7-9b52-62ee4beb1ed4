{"id": "EH-1", "name": "Try-Catch with Context", "category": "error-handling", "purpose": "Add contextual information to errors while preserving the original error details", "keyPrinciples": ["Preserve original error", "Add operation context", "Include relevant data", "Use consistent error types", "Provide appropriate logging"], "useCases": ["Service boundaries", "External API calls", "Data processing operations", "User input handling"], "relatedPatterns": ["EH-2", "EH-4", "SEC-5"], "dependencies": [], "examples": ["userService.getUserData", "processUserData"], "mainFile": "EH-1.md", "implementationLanguages": ["typescript", "javascript"], "created": "2023-03-22", "lastUpdated": "2023-03-22"}