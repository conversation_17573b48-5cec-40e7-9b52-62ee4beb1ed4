# Python Coding Standards

## PEP 8 Style Guide

Follow the PEP 8 style guide for Python code:

1. Use 4 spaces for indentation
2. Use snake_case for variable and function names
3. Use CamelCase for class names
4. Use UPPERCASE for constants
5. Keep line length under 79 characters (88 with Black formatter)
6. Use docstrings for modules, classes, and functions
7. Import statements should be on separate lines
8. Group imports in the following order:
   - Standard library imports
   - Related third-party imports
   - Local application/library specific imports
9. Use absolute imports rather than relative imports
10. Use spaces around operators and after commas

## Type Hints

Use type hints for function parameters and return values:

```python
def greeting(name: str) -> str:
    return f"Hello, {name}"
```

## Docstrings

Use Google-style docstrings:

```python
def example_function(param1: str, param2: int) -> bool:
    """Short description of the function.
    
    Longer description explaining the function's purpose and behavior.
    
    Args:
        param1: Description of param1
        param2: Description of param2
        
    Returns:
        Description of return value
        
    Raises:
        ValueError: When param1 is empty
    """
    if not param1:
        raise ValueError("param1 cannot be empty")
    # Function implementation
    return True
```

## Testing

1. Write unit tests using pytest
2. Aim for high test coverage
3. Use fixtures for test setup
4. Mock external dependencies
5. Use parameterized tests for multiple test cases 