# Development Containers

## Requirements

All development for this project can also be done within a development container environment.

1. Use the provided `.devcontainer` configuration
2. Ensure Docker is installed and running on your system
3. Use VS Code or Cursor with the Dev Containers extension
4. Run all commands within the container environment
5. Keep container configurations in sync with `shell.nix`

## Benefits

- Complete isolation from host system
- Consistent development environment across platforms
- Pre-configured development tools and dependencies
- Simpler onboarding for new team members
- Matches production environment more closely
- Works alongside Nix shell for flexibility

## Configuration

The development container should:

1. Use the same base image as production when possible
2. Include all development dependencies
3. Mount the project directory as a volume
4. Configure appropriate port forwarding
5. Set up proper environment variables
6. Include any required SSH keys or credentials securely

## Container Workflow

When using development containers:

1. Open the project in VS Code/Cursor
2. Select "Reopen in Container" when prompted
3. Wait for container build to complete
4. Use the integrated terminal for all commands
5. Commit container configuration changes with project code

## Verification

To verify your development container is working correctly, check:

1. The container is running (`docker ps` shows the container)
2. All required tools are available in the terminal
3. The project builds and runs successfully
4. File changes on the host are reflected in the container
5. Development servers can be accessed via forwarded ports 