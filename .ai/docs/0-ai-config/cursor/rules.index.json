{"rules": [{"id": "001", "name": "Coding Standards", "file": "rules/001_coding_standards.mdc", "description": "General coding standards for all code in the project", "categories": ["standards", "coding", "general"]}, {"id": "002", "name": "Python Standards", "file": "rules/002_python_standards.mdc", "description": "Python-specific coding standards and best practices", "categories": ["standards", "coding", "python"]}, {"id": "003", "name": "JavaScript Standards", "file": "rules/003_javascript_standards.mdc", "description": "JavaScript and TypeScript coding standards and best practices", "categories": ["standards", "coding", "javascript", "typescript"]}, {"id": "004", "name": "Nix Shell Usage", "file": "rules/004_nix_shell_usage.mdc", "description": "Requirements and guidance for using Nix shell for development", "categories": ["environment", "nix", "shell"]}, {"id": "005", "name": "Development Containers", "file": "rules/005_devContainers.mdc", "description": "Guidelines for using development containers for isolated environments", "categories": ["environment", "docker", "container", "devops"]}], "settings": {"file": "settings.json", "description": "Cursor IDE settings for this project"}, "lastUpdated": "2023-03-22"}