# Cursor IDE Integration

This document provides guidelines for configuring Cursor IDE to work effectively with this project's AI-assisted development workflow.

## Cursor Overview

[Cursor](https://cursor.sh/) is an IDE built on top of VS Code that integrates AI capabilities directly into the development environment. It enables:

- AI-powered code generation
- Contextual assistance for development tasks
- Semantic code search
- Natural language code manipulation
- Integrated chat for complex questions

## Setup Instructions

### 1. Installation

1. Download and install Cursor from [https://cursor.sh/](https://cursor.sh/)
2. Open this project in Cursor
3. Ensure you have the latest version installed

### 2. Project-Specific Configuration

Create a `.cursor.json` file in the project root with the following configuration:

```json
{
  "aiTools": {
    "enableAITools": true,
    "enableAIChat": true,
    "enableAIAutocomplete": true,
    "enableAICommand": true
  },
  "contextProvider": {
    "priorityPaths": [
      ".ai/docs/**/*",
      "src/**/*"
    ],
    "ignorePaths": [
      "node_modules/**/*",
      "dist/**/*",
      "build/**/*",
      ".git/**/*"
    ]
  },
  "codeSearch": {
    "enabled": true,
    "maxResults": 50
  },
  "chat": {
    "defaultPrompt": "You are assisting with development on this project. Use the standards and patterns defined in the .ai/docs directory.",
    "historyEnabled": true
  }
}
```

### 3. Cursor Rules Setup

Create a `.cursorrules` file in the `.ai/docs/0-ai-config/cursor/` directory for AI guidance:

```
I am helping with the development of this project.

I should follow these principles:
1. Use explicit variable names over short ambiguous ones
2. Follow the project's consistent coding style
3. Prioritize performance in suggested changes
4. Take a security-first approach
5. Consider test coverage for new/modified code
6. Implement robust error handling and logging
7. Encourage modular design principles
8. Ensure version compatibility
9. Avoid magic numbers
10. Consider edge cases
11. Use assertions where appropriate

When providing code assistance, I should:
1. Reference patterns in .ai/docs/3-development/patterns/
2. Follow error handling in .ai/docs/3-development/patterns/error_handling.md
3. Adhere to documentation standards in .ai/docs/1-context/standards/documentation_standards.md
4. Consider security checklist in .ai/docs/4-acceptance/security/checklist.md
5. Follow performance guidelines in .ai/docs/4-acceptance/performance/guidelines.md

I should look at project-specific code patterns before suggesting implementations.
```

### 4. Key Keyboard Shortcuts

Configure these keyboard shortcuts for AI-assisted development:

| Action | Shortcut |
|--------|----------|
| AI Chat | Ctrl+Shift+A |
| Generate Code (Inline) | Ctrl+K |
| Edit Code with Natural Language | Ctrl+L |
| Search Codebase | Ctrl+Shift+F |
| Fix Code Issues | Ctrl+Shift+. |
| Explain Code | Ctrl+Shift+/ |

## Recommended Workflows

### Feature Implementation Workflow

1. Open the feature specification in `.ai/docs/2-technical-design/features/`
2. Use Cursor to create implementation files based on the spec
3. Use AI command (Ctrl+K) to generate scaffolding
4. Iteratively refine implementation with AI assistance
5. Validate against standards in `.ai/docs/4-acceptance/`

### Debugging Workflow

1. Identify the error or issue
2. Use AI chat to help understand potential causes
3. Use "Fix Code Issues" (Ctrl+Shift+.) for quick fixes
4. For complex issues, use chat with relevant code snippets
5. Document solutions in `.ai/docs/3-development/support/debug_history/`

### Code Review Workflow

1. Use Cursor to review changes against project standards
2. Press Ctrl+Shift+/ on complex code for explanations
3. Use AI chat to suggest improvements
4. Reference specific standards from `.ai/docs/` when needed
5. Document review findings for team knowledge sharing

## Project-Specific Semantic Search

When using Cursor's semantic search:

1. Include `.ai/docs/` in your search scope
2. Use specific terminology from the project glossary
3. Reference feature names from `.ai/docs/2-technical-design/features/`
4. Search for implementation patterns with consistent terminology

Example searches:
- "How do we handle authentication errors in this project?"
- "Find examples of API endpoint implementation"
- "Show me the error handling pattern for database operations"

## Tips for Effective AI Usage in Cursor

1. **Include Context**: Start chat sessions with relevant context
2. **Be Specific**: Request specific implementations or changes
3. **Reference Standards**: Mention project standards in your requests
4. **Iterative Refinement**: Start with basic implementation, then refine
5. **Learn the Commands**: Master keyboard shortcuts for efficiency
6. **Save Useful Prompts**: Save effective prompts for reuse

## Troubleshooting

If Cursor AI is not providing relevant assistance:

1. Check that `.cursor.json` is properly configured
2. Ensure the `.cursorrules` file is accessible
3. Verify that context directories are correctly prioritized
4. Restart Cursor to refresh configuration
5. Update to the latest Cursor version
6. Check that AI services are available (see Cursor status page)

## Recommended Extensions

These extensions enhance Cursor's capabilities for this project:

1. **GitLens**: Enhanced Git integration
2. **ESLint/TSLint**: Code quality enforcement
3. **Prettier**: Code formatting
4. **Path Intellisense**: Path autocompletion
5. **Better Comments**: Enhanced documentation

---

*Last Updated: [Current Date]* 