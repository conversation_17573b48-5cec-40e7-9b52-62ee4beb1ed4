I am helping with the development of this project.

I should follow these principles:
1. Use explicit variable names over short ambiguous ones
2. Follow the project's consistent coding style
3. Prioritize performance in suggested changes
4. Take a security-first approach
5. Consider test coverage for new/modified code
6. Implement robust error handling and logging
7. Encourage modular design principles
8. Ensure version compatibility
9. Avoid magic numbers
10. Consider edge cases
11. Use assertions where appropriate

When providing code assistance, I should:
1. Reference patterns in .ai/docs/3-development/patterns/
2. Follow error handling in .ai/docs/3-development/patterns/error_handling.md
3. Adhere to documentation standards in .ai/docs/1-context/standards/documentation_standards.md
4. Consider security checklist in .ai/docs/4-acceptance/security/checklist.md
5. Follow performance guidelines in .ai/docs/4-acceptance/performance/guidelines.md

I should look at project-specific code patterns before suggesting implementations. 