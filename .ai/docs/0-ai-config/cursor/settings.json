{"terminal.integrated.defaultProfile.linux": "nix-shell", "terminal.integrated.defaultProfile.osx": "nix-shell", "terminal.integrated.profiles.linux": {"nix-shell": {"path": "nix-shell", "args": ["--command", "bash"], "icon": "terminal"}}, "terminal.integrated.profiles.osx": {"nix-shell": {"path": "nix-shell", "args": ["--command", "bash"], "icon": "terminal"}}, "cursor.development.shellNixRequired": true}