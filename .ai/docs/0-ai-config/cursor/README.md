# Cursor Configuration

@context-card: {
  "type": "configuration",
  "tool": "cursor",
  "purpose": "IDE configuration for AI-assisted development",
  "keyFiles": [
    "settings.json",
    "rules.index.json", 
    "rules/"
  ]
}

## Overview

This directory contains configuration for Cursor IDE, optimized for AI-assisted development on this project.

## Key Components

- **Settings**: IDE settings in `settings.json`
- **Rules**: Coding standards and practices in `rules/`
- **Index**: Metadata about rules in `rules.index.json`

## Rules Summary

| ID | Name | Purpose |
|----|------|---------|
| 001 | Coding Standards | General coding standards for all languages |
| 002 | Python Standards | Python-specific coding conventions |
| 003 | JavaScript Standards | JavaScript/TypeScript conventions |
| 004 | Nix Shell Usage | Development environment requirements |

## Integration

The configuration ensures:

1. Consistent development environment using Nix shell
2. Standardized coding practices across languages
3. AI-optimized workflows in the IDE
4. Context-aware AI assistance

## Usage

Cursor automatically loads these configurations when opening the project. No manual setup is required.

## Related Files

- Main cursor configuration: `.cursorrules`
- Development environment: `shell.nix`
- Token optimization: `AI-CFG/TOKEN_OPTIMIZATION.md` 