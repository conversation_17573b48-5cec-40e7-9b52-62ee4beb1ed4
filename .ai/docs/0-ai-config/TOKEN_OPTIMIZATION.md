# AI Token Optimization Guide

This document provides strategies for minimizing token usage while maintaining context quality when working with AI assistants.

## Key Optimization Strategies

1. **Use index files with summaries** - Provide quick overviews without loading full content
2. **Reference patterns with shorthands** - Use standard codes for common patterns
3. **Structure paths semantically** - Make file paths descriptive to reduce explanatory text
4. **Implement tiered documentation** - Separate core principles from implementation details
5. **Create focused micro-documents** - Break large documents into smaller, targeted files
6. **Use context hints in code** - Reference documents without duplicating content
7. **Add context cards** - Provide compact metadata about documents
8. **Use context-specific templates** - Load only relevant documentation sections

## Implementation Reference

| Strategy | Implementation | Documentation |
|----------|----------------|---------------|
| Index Files | Directory summary files | `.ai/docs/3-development/patterns/INDEX.md` |
| Pattern Shorthands | Standard code references | `.ai/docs/0-ai-config/SHORTHAND.md` |
| Semantic Paths | Directory structure | `.ai/STRUCTURE.md` |
| Tiered Documentation | Core/Implementation/Examples | `.ai/docs/4-acceptance/security/tiered/` |
| Micro-Documents | Pattern files | `.ai/docs/3-development/patterns/error_handling/` |
| Context Hints | Code comment references | `.ai/docs/3-development/code_hints.md` |
| Context Cards | JSON metadata files | `.ai/docs/3-development/patterns/error_handling/EH-1.context.json` |
| Contextual Templates | Task-specific templates | `.ai/docs/0-ai-config/claude/prompt_templates.md` |

## Best Practices

### When Creating Documentation

1. **Create focused documents** - One concept per file with clear scope
2. **Use consistent naming** - Follow established patterns for easy reference
3. **Structure content hierarchically** - Most important information first
4. **Include summary sections** - Provide quick overviews at the beginning
5. **Cross-reference rather than duplicate** - Link to related information

### When Working with AI

1. **Reference specific paths** - Point to exact documentation locations
2. **Use pattern shorthands** - Reference patterns by their codes
3. **Include only necessary context** - Avoid copying entire documents
4. **Use appropriate templates** - Choose context-specific templates
5. **Leverage context hints** - Let code comments provide references

## Token Efficiency Examples

### Inefficient Approach

```
I need to implement error handling for the user authentication service. 
Please show me how to handle validation errors, database connection errors, 
and authentication failures. Include comprehensive error handling with logging, 
user-friendly messages, and proper error propagation.
```

### Efficient Approach

```
Implement error handling for user authentication service using:
- [EH-1] for validation errors
- [EH-2] for database errors
- [SEC-AUTH-IMPL] for auth failures

See `.ai/docs/3-development/patterns/error_handling/` for patterns.
```

## Document Structure Optimization

### Traditional Document

```markdown
# Authentication Implementation

## Overview
[detailed explanation...]

## Requirements
[comprehensive list...]

## Implementation Details
[complete code examples...]

## Best Practices
[extensive list of practices...]

## Security Considerations
[detailed security guidance...]
```

### Optimized Structure

```markdown
# Authentication Implementation

@context-card: {
  "type": "implementation",
  "domain": "security",
  "patterns": ["SEC-1", "EH-1"],
  "relatedDocs": ["core/authentication.md", "examples/authentication.md"]
}

## Core Requirements
[essential points only]

## Implementation
[key implementation points with pattern references]

## References
- Core principles: [SEC-AUTH-CORE]
- Examples: [SEC-AUTH-EXAMPLES]
- Error handling: [EH-1], [EH-2]
```

## Context Loading Workflow

When an AI assistant needs information:

1. Check INDEX files first for quick overviews
2. Look for context cards to get metadata
3. Follow references to related documents
4. Load core principles before implementation details
5. Check for context hints in code

## Measuring Token Efficiency

Use these metrics to evaluate documentation efficiency:

1. **Information Density** - Key points per token
2. **Cross-Reference Ratio** - References vs. duplicated content
3. **Context Coverage** - Essential context included vs. total documentation
4. **Navigation Efficiency** - Steps needed to find relevant information 