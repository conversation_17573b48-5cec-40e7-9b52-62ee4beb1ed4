# AI-Assisted Development Workflow

This document outlines the standardized development workflow for AI-assisted development, combining structured processes with AI tools to enhance development efficiency and quality.

## Workflow Overview

```mermaid
flowchart TD
    A[1. Bootstrap Project] --> B[2. Define Context & Requirements]
    B --> C[3. Document Feature]
    C --> D[4. Implement Feature]
    D --> E[5. Validate Standards]
    E --> F{Validation Passed?}
    F -->|No| G[7. Revise Feature]
    G --> E
    F -->|Yes| H[6. Create Validation Report]
    H --> I[8. Compliance Review]
    I --> J[Feature Complete]
    J -.-> C[Start Next Feature]
```

## AI Integration Points

AI assistants are integrated throughout the development workflow:

- **Planning**: AI helps break down requirements and identify edge cases
- **Design**: AI assists with architecture diagrams and design patterns
- **Implementation**: AI generates code scaffolding and implements features
- **Review**: AI performs preliminary code reviews
- **Testing**: AI helps generate test cases and debug failures
- **Documentation**: AI maintains documentation as code changes

## Workflow Steps

### 1. Bootstrap Project

Initialize a new project with the proper structure:

- Create the `.ai` directory and its subdirectories
- Set up initial documentation files
- Configure development environment
- Initialize version control

**Key Tasks for AI Assistance:**
- Generate project structure based on templates
- Create initial documentation files
- Set up configuration files for development tools
- Establish code standards and linting rules

### 2. Define Context & Requirements

Determine the project's scope, goals, and requirements:

- Document project context in `.ai/docs/1-context/project/`
- Define project conventions in `.ai/docs/1-context/standards/`
- Identify technical requirements in `.ai/docs/2-technical-design/requirements/`

**Key Tasks for AI Assistance:**
- Help clarify and formalize requirements
- Identify potential edge cases and considerations
- Suggest appropriate technologies and frameworks
- Generate user stories and acceptance criteria

### 3. Document Feature

Before implementation, document the feature:

- Create a feature specification in `.ai/docs/2-technical-design/features/`
- Include requirements, acceptance criteria, and design considerations
- Reference applicable standards
- Get stakeholder approval on the specification

**Key Tasks for AI Assistance:**
- Draft feature specifications based on requirements
- Generate architecture diagrams using Mermaid
- Suggest design patterns appropriate for the requirements
- Create component interfaces and API contracts

**Documentation Template:**
```markdown
# Feature: [Feature Name]

## Overview
Brief description of the feature

## Requirements
- Functional requirement 1
- Functional requirement 2

## Acceptance Criteria
- [ ] Criterion 1
- [ ] Criterion 2

## Design Considerations
Notes on implementation approach

## Technology Stack
- Frontend: [technologies]
- Backend: [technologies]
- Database: [technologies]

## Applicable Standards
- Link to relevant standards

## Implementation Plan
- Step 1: [description]
- Step 2: [description]
```

### 4. Implement Feature

Develop the feature according to the specification:

- Follow project conventions and coding standards
- Implement automated tests
- Document code appropriately
- Use AI assistance as needed, documenting in `.ai/docs/3-development/support/`

**Key Tasks for AI Assistance:**
- Generate code scaffolding based on design documents
- Implement features following project coding standards
- Write unit tests and integration tests
- Generate API documentation from code and comments
- Suggest optimizations and performance improvements

**Best Practices:**
- Commit frequently with descriptive messages
- Reference feature documentation in commits
- Keep changes focused on the specific feature
- Use consistent coding patterns documented in `.ai/docs/3-development/patterns/`

### 5. Validate Standards

Verify that the implementation meets all applicable standards:

- Run automated validation tools
- Perform manual checks against standards
- Review against feature specification and acceptance criteria
- Conduct code review if applicable

**Key Tasks for AI Assistance:**
- Perform preliminary code reviews
- Check for adherence to coding standards
- Identify potential bugs or edge cases
- Evaluate performance considerations
- Detect security vulnerabilities
- Verify test coverage

### 6. Create Validation Report

Document the validation results:

- Record test results and coverage metrics
- Document code review findings
- Note any standards deviations and justifications
- Store the report in `.ai/docs/4-acceptance/`

**Key Tasks for AI Assistance:**
- Generate validation reports from test results
- Summarize code review findings
- Create documentation for validated features
- Update project status documentation

### 7. Revise Feature

If validation fails, revise the implementation:

- Address issues identified during validation
- Update tests as needed
- Document changes made
- Re-validate after changes

**Key Tasks for AI Assistance:**
- Analyze validation failures
- Suggest fixes for identified issues
- Update code to address problems
- Regenerate tests for new edge cases
- Update documentation to reflect changes

### 8. Compliance Review

Final review to ensure all standards and requirements are met:

- Verify all acceptance criteria are satisfied
- Check compliance with project standards
- Confirm documentation is complete and accurate
- Approve feature for release

**Key Tasks for AI Assistance:**
- Verify compliance with all requirements
- Check documentation completeness
- Generate final feature documentation
- Update relevant cheatsheets and patterns
- Contribute to project knowledge base

## Best Practices for Working with AI

### Effective Prompting

1. **Be specific**: Provide clear context and specific requirements
2. **Include examples**: Show examples of desired output when possible
3. **Provide feedback**: Guide AI to improve responses
4. **Use iterative refinement**: Start broad and refine with follow-up prompts

### Context Management

1. Keep AI informed of project status and recent changes
2. Reference relevant documentation in prompts
3. Update AI on decisions made outside the AI interaction
4. Maintain a record of important AI interactions in `.ai/docs/3-development/support/`

### Quality Assurance

1. Always review AI-generated code before committing
2. Validate AI suggestions against project requirements
3. Use AI for initial reviews, but ensure human oversight
4. Document AI-assisted decisions in `.ai/docs/2-technical-design/decisions.md`

## Multi-Tool Support

This workflow supports multiple AI tools:

- **Claude**: Advanced reasoning and content generation
- **Cursor**: Real-time coding assistance
- **Cline**: Command-line AI interactions
- **GitHub Copilot**: Inline code suggestions

Each tool has its specific configuration in `.ai/docs/0-ai-config/`. 