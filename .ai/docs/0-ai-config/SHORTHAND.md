# Pattern Shorthand Reference

This document defines abbreviated codes for common patterns to reduce token usage in documentation and discussions.

## Using Pattern Shorthands

When referencing patterns, use the shorthand code in square brackets:
- `[EH-1]` references "Error Handling Pattern 1: Try-Catch with Context"
- `[SEC-2]` references "Security Pattern 2: Permission-Based Authorization"

## Pattern Categories

### Error Handling (EH)
- **EH-1**: Try-Catch with Context
- **EH-2**: Result Type Pattern
- **EH-3**: Middleware Error Handling
- **EH-4**: Graceful Degradation
- **EH-5**: Circuit Breaker

### API Design (API)
- **API-1**: Resource-Based Routes
- **API-2**: Controller-Service-Repository
- **API-3**: Input Validation Pipeline
- **API-4**: API Versioning
- **API-5**: HATEOAS Links

### UI Patterns (UI)
- **UI-1**: Container/Presentation Pattern
- **UI-2**: Compound Components
- **UI-3**: Render Props/Hooks
- **UI-4**: State Machines
- **UI-5**: Component Composition

### Database Patterns (DB)
- **DB-1**: Repository Pattern
- **DB-2**: Unit of Work
- **DB-3**: Optimistic Concurrency
- **DB-4**: Query Object
- **DB-5**: Database Migrations

### Security Patterns (SEC)
- **SEC-1**: Authentication Gateway
- **SEC-2**: Permission-Based Authorization
- **SEC-3**: Secure Data Pipeline
- **SEC-4**: Input Sanitization
- **SEC-5**: Audit Logging

### Performance Patterns (PERF)
- **PERF-1**: Caching Strategy
- **PERF-2**: Lazy Loading
- **PERF-3**: Pagination
- **PERF-4**: Resource Pooling
- **PERF-5**: Asynchronous Processing

### Testing Patterns (TEST)
- **TEST-1**: Arrange-Act-Assert
- **TEST-2**: Test Doubles (Mock/Stub/Spy)
- **TEST-3**: Property-Based Testing
- **TEST-4**: Integration Test Fixtures
- **TEST-5**: Behavior-Driven Development

### Architecture Patterns (ARCH)
- **ARCH-1**: Microservices
- **ARCH-2**: Event-Driven Architecture
- **ARCH-3**: CQRS Pattern
- **ARCH-4**: Hexagonal Architecture
- **ARCH-5**: Domain-Driven Design

## Directory Shorthands

- **AI-CFG**: `.ai/docs/0-ai-config/`
- **CTX**: `.ai/docs/1-context/`
- **TECH**: `.ai/docs/2-technical-design/`
- **DEV**: `.ai/docs/3-development/`
- **ACC**: `.ai/docs/4-acceptance/`

## Documentation Types

- **IDX**: Index file
- **REF**: Reference document
- **SPEC**: Specification
- **GUIDE**: How-to guide
- **META**: Metadata document 