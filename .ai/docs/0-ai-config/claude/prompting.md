# Claude Prompting Guide

This document provides guidelines for effectively prompting <PERSON> when working on this project. Following these patterns will result in more consistent, accurate, and useful responses.

## Core Prompting Principles

1. **Be Specific and Clear**: Provide precise instructions and context
2. **Include Examples**: Show desired formats and outputs when possible
3. **Structure Requests**: Break complex requests into steps
4. **Provide Context**: Give <PERSON> necessary background information
5. **Use Consistent Terminology**: Align with project terminology
6. **Iterative Refinement**: Start broad, then refine with follow-up prompts

## Effective Prompting Patterns

### Pattern 1: Context-Action-Format

Structure prompts with clear context, specific action requests, and format guidance:

```
CONTEXT: I'm working on the user authentication feature in the AccountController.cs file.

ACTION: Please create a method to validate user credentials against our database and return a JWT token if valid.

FORMAT: Follow our standard C# error handling pattern with try/catch blocks and include XML documentation.
```

### Pattern 2: Example-Based Prompting

Show Claude examples of the desired output format:

```
I need a React component for a form that follows our project patterns. Here's an example of how we structure components:

```tsx
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>Field } from '@/components/ui';
import { useFormValidation } from '@/hooks';

export function ExampleComponent({ initialData, onSubmit }) {
  const [formData, setFormData] = useState(initialData);
  const { errors, validate } = useFormValidation();
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (validate(formData)) {
      await onSubmit(formData);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      {/* Form content */}
    </form>
  );
}
```

Please create a UserProfileForm component following this pattern that includes fields for name, email, and bio.
```

### Pattern 3: Step-by-Step Instructions

Break down complex tasks into sequential steps:

```
I need help implementing the product search feature. Please:

1. First, show me how to define the search endpoint in our Express router
2. Then, create the controller function that handles the search
3. Next, implement the database query using our Prisma ORM
4. Finally, show how to format and return the search results
```

### Pattern 4: Bounded Creativity

Specify constraints while allowing creativity in certain areas:

```
I need a new error handling middleware for our Express application.

CONSTRAINTS:
- Must use our ApplicationError class hierarchy
- Must log errors using our structured logger
- Must return JSON responses with our standard error format

CREATIVE FREEDOM:
- How to categorize different types of errors
- Implementing special handling for specific error types
- Adding contextual information to error responses
```

## Project-Specific Prompting Guidelines

### Code References

When referring to existing code, use this format to help Claude locate the code accurately:

```
In file src/components/UserProfile.tsx, around line 45, there's a function that handles form submission. I need to modify it to...
```

### Component Patterns

When requesting new components, always specify:

1. The component's purpose and responsibilities
2. Required props and their types
3. State management approach
4. Error handling requirements
5. Integration with existing components

Example:

```
Please create a ProductCard component that:
1. Accepts product data (id, name, price, image) as props
2. Handles lazy loading of images
3. Includes an "Add to Cart" button that calls the useCart hook
4. Shows a loading state while product data is being fetched
5. Will be used within the ProductGrid component
```

### API Endpoints

When requesting API endpoint implementation, always specify:

1. The HTTP method and route
2. Request parameters and body format
3. Authorization requirements
4. Response format and status codes
5. Error handling expectations

Example:

```
I need to implement a new API endpoint:

- POST /api/orders
- Requires authentication token in Authorization header
- Accepts order data in request body (items array, shipping address, payment method)
- Should validate input, create order in database, and process payment
- Returns 201 Created with order ID on success
- Returns appropriate error codes (400, 401, 403, 500) with error messages on failure
```

## Special Instructions for Different Tasks

### Debugging Assistance

When asking for debugging help, provide:

1. The error message or unexpected behavior
2. The relevant code snippet
3. What you've already tried
4. Expected vs. actual behavior

Example:

```
I'm getting this error when submitting the user registration form:
"TypeError: Cannot read property 'email' of undefined"

Here's the submission handler:
```jsx
const handleSubmit = async (e) => {
  e.preventDefault();
  try {
    const response = await api.users.register(userData);
    onSuccess(response.user);
  } catch (error) {
    setError(error.message);
  }
};
```

I've verified that userData is defined before the API call, but it seems like the response object doesn't have the structure I expect.
```

### Code Reviews

When requesting code reviews, ask Claude to focus on specific aspects:

```
Please review this authentication implementation for:
1. Security vulnerabilities
2. Performance issues
3. Adherence to our coding standards
4. Error handling completeness
5. Edge cases I might have missed

```tsx
// Authentication code here
```
```

### Architecture Decisions

When seeking architecture guidance, provide context on requirements and constraints:

```
I need to implement a notification system for our application with these requirements:
- Must support email, SMS, and in-app notifications
- Notifications should be queued and processed asynchronously
- Users should be able to set notification preferences
- System must be scalable to handle thousands of notifications per minute

What architecture would you recommend, and how should I structure the components?
```

## Using Project Context in Prompts

Reference project-specific resources to give Claude additional context:

```
Following our error handling pattern in `.ai/docs/3-development/patterns/error_handling.md`, please help me implement error handling for the payment processing feature.
```

```
Based on the API design in `.ai/docs/2-technical-design/features/user-management/specification.md`, please implement the user profile update endpoint.
```

## Common Pitfalls to Avoid

1. **Vague Requests**: "Improve this code" vs. "Optimize this database query for performance"
2. **Missing Context**: Not providing enough background information
3. **Inconsistent Terminology**: Using different terms for the same concept
4. **Overwhelming Requests**: Asking for too much in a single prompt
5. **Lack of Constraints**: Not specifying important requirements or limitations

---

*Last Updated: [Current Date]* 