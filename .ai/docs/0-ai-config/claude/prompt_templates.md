# Context-Specific Prompting Templates

This document provides optimized templates for prompting <PERSON> in specific contexts. These templates help reduce token usage by loading only relevant documentation sections.

## How to Use Templates

1. Choose the appropriate template for your task
2. Replace placeholders (marked with `{placeholder}`) with your content
3. Copy the template into your prompt to Claude

## Template Categories

### Feature Implementation Template

```
CONTEXT: Implementing feature "{feature_name}"
FEATURE_SPECS: {brief_summary_of_feature}

TASK: Please help me implement the {component_name} component for this feature.

RELEVANT_PATTERNS: 
- [EH-1] Try-Catch with Context
- [API-2] Controller-Service-Repository
- {other_relevant_patterns}

IMPLEMENTATION_REQUIREMENTS:
{requirements}

EXISTING_CODE_STRUCTURE:
{brief_description_of_existing_code}
```

### Code Review Template

```
CONTEXT: Reviewing code for {feature_name}

CODE_TO_REVIEW:
```{language}
{code_snippet}
```

REVIEW_FOCUS:
- Security concerns
- Performance optimizations
- Pattern adherence
- Error handling
- {other_focus_areas}

RELEVANT_STANDARDS:
- {list_relevant_patterns_or_standards}
```

### Debugging Template

```
CONTEXT: Debugging issue in {component_name}

ERROR_DETAILS:
{error_message_or_symptom}

RELEVANT_CODE:
```{language}
{code_snippet}
```

ATTEMPTED_SOLUTIONS:
{what_I've_tried_so_far}

ENVIRONMENT:
{relevant_environment_details}
```

### Architecture Design Template

```
CONTEXT: Designing architecture for {feature_or_system_name}

REQUIREMENTS:
{key_requirements}

CONSTRAINTS:
{technical_or_business_constraints}

EXISTING_ARCHITECTURE:
{brief_description_of_current_architecture}

RELEVANT_PATTERNS:
- [ARCH-{number}] {pattern_name}
- {other_relevant_patterns}
```

### Documentation Template

```
CONTEXT: Creating documentation for {component_or_feature}

DOCUMENTATION_TYPE: {API|User Guide|Internal Docs|etc}

COMPONENT_PURPOSE:
{brief_description}

CORE_FUNCTIONALITY:
{key_functionality_points}

INTERFACES:
{main_interfaces_or_apis}
```

### Security Review Template

```
CONTEXT: Security review for {feature_or_component}

COMPONENT_DETAILS:
{brief_description}

SECURITY_DOMAINS_TO_REVIEW:
- Authentication
- Authorization
- Data Protection
- {other_security_areas}

IMPLEMENTATION_DETAILS:
```{language}
{code_snippet}
```

RELEVANT_SECURITY_STANDARDS:
- [SEC-{number}] {standard_name}
- {other_standards}
```

### Performance Optimization Template

```
CONTEXT: Optimizing performance for {component_or_operation}

CURRENT_PERFORMANCE:
{metrics_or_description_of_current_performance}

PERFORMANCE_GOALS:
{target_metrics_or_improvements}

IMPLEMENTATION_DETAILS:
```{language}
{code_snippet}
```

RELEVANT_PERFORMANCE_PATTERNS:
- [PERF-{number}] {pattern_name}
- {other_patterns}
```

## Custom Template for {Project-Specific-Need}

```
CONTEXT: {specific_context}

{custom_sections_and_requirements}

RELEVANT_DOCUMENTATION:
- {paths_to_relevant_docs}
```

---

These templates are designed to provide sufficient context while minimizing token usage. Adjust as needed for your specific requirements. 