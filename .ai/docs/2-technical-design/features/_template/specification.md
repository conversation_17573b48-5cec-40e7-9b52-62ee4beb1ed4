# Feature: [Feature Name]

## Overview

[Provide a brief description of the feature, including its purpose and benefits.]

## Requirements

### Functional Requirements

- [Functional requirement 1]
- [Functional requirement 2]
- [Functional requirement 3]

### Non-Functional Requirements

- **Performance**: [Performance requirements]
- **Security**: [Security requirements]
- **Scalability**: [Scalability requirements]
- **Maintainability**: [Maintainability requirements]

## Acceptance Criteria

- [ ] [Criterion 1]
- [ ] [Criterion 2]
- [ ] [Criterion 3]

## User Stories

### Primary User Story

As a [user type], I want to [action], so that [benefit].

### Additional User Stories

- As a [user type], I want to [action], so that [benefit].
- As a [user type], I want to [action], so that [benefit].

## Technical Design

### Architecture

[Describe the architectural approach for this feature, including components and interactions.]

```mermaid
graph TD
    A[Component A] --> B[Component B]
    B --> C[Component C]
    A --> C
```

### Data Model

[Describe data structures, entities, and relationships.]

```mermaid
classDiagram
    Class01 <|-- AveryLongClass : Cool
    Class03 *-- Class04
    Class05 o-- Class06
    Class07 .. Class08
    Class09 --> C2 : Where am I?
    Class09 --* C3
    Class09 --|> Class07
    Class07 : equals()
    Class07 : Object[] elementData
    Class01 : size()
    Class01 : int chimp
    Class01 : int gorilla
    Class08 <--> C2: Cool label
```

### API Design

[Document APIs, endpoints, or interfaces for this feature.]

#### Endpoints

| Method | Endpoint | Description | Request | Response |
|--------|----------|-------------|---------|----------|
| GET    | /api/resource | Get resource | - | Resource object |
| POST   | /api/resource | Create resource | Resource object | Created resource |

#### Data Contracts

```typescript
interface Resource {
  id: string;
  name: string;
  description: string;
  createdAt: Date;
}
```

## Implementation Plan

1. [Step 1: Description]
2. [Step 2: Description]
3. [Step 3: Description]

## Testing Strategy

### Unit Testing

[Describe unit testing approach, including key test cases.]

### Integration Testing

[Describe integration testing approach.]

### End-to-End Testing

[Describe end-to-end testing approach.]

## Performance Considerations

[Document performance considerations and optimization strategies.]

## Security Considerations

[Document security considerations and mitigations.]

## Dependencies

- [Dependency 1]
- [Dependency 2]

## Rollout Plan

[Describe how the feature will be rolled out, including phasing if applicable.]

## Monitoring and Telemetry

[Describe how the feature will be monitored in production.]

## Documentation Requirements

[List documentation that needs to be created or updated for this feature.]

## Open Questions

- [Question 1]
- [Question 2]

---

*Last Updated: [Date]*  
*Author: [Name]* 